/* ==========================================================================
   Header Components - Professional Fortune 500 Style
   ========================================================================== */

/* Site Header */
.site-header {
    background-color: var(--vp-background);
    border-bottom: 1px solid var(--vp-border-light);
    box-shadow: var(--vp-shadow-sm);
    position: sticky;
    top: 0;
    z-index: 1000;
    transition: all var(--vp-transition-normal);
}

.site-header .vp-container {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding-top: var(--vp-space-4);
    padding-bottom: var(--vp-space-4);
    min-height: 80px;
}

/* Site Branding */
.site-branding {
    display: flex;
    align-items: center;
    gap: var(--vp-space-3);
}

.site-title {
    margin: 0;
    font-size: var(--vp-font-size-2xl);
    font-weight: var(--vp-font-weight-bold);
    line-height: var(--vp-line-height-none);
}

.site-title a {
    color: var(--vp-text-primary);
    text-decoration: none;
    transition: color var(--vp-transition-fast);
}

.site-title a:hover {
    color: var(--vp-primary);
}

.site-description {
    margin: 0;
    font-size: var(--vp-font-size-sm);
    color: var(--vp-text-tertiary);
    font-weight: var(--vp-font-weight-normal);
}

.custom-logo-link {
    display: block;
    line-height: 0;
}

.custom-logo {
    height: auto;
    max-height: 60px;
    width: auto;
    transition: opacity var(--vp-transition-fast);
}

.custom-logo:hover {
    opacity: 0.8;
}

/* Main Navigation */
.main-navigation {
    display: flex;
    align-items: center;
}

.primary-menu {
    display: flex;
    align-items: center;
    list-style: none;
    margin: 0;
    padding: 0;
    gap: var(--vp-space-1);
}

.primary-menu li {
    margin: 0;
    position: relative;
}

.primary-menu a {
    display: block;
    padding: var(--vp-space-3) var(--vp-space-4);
    color: var(--vp-text-secondary);
    text-decoration: none;
    font-weight: var(--vp-font-weight-medium);
    font-size: var(--vp-font-size-sm);
    border-radius: var(--vp-radius-md);
    transition: all var(--vp-transition-fast);
    position: relative;
}

.primary-menu a:hover,
.primary-menu a:focus {
    color: var(--vp-primary);
    background-color: var(--vp-primary-light);
}

.primary-menu .current-menu-item > a,
.primary-menu .current_page_item > a {
    color: var(--vp-primary);
    background-color: var(--vp-primary-light);
}

/* Mobile Menu Toggle */
.menu-toggle {
    display: none;
    background: none;
    border: none;
    padding: var(--vp-space-2);
    cursor: pointer;
    border-radius: var(--vp-radius-md);
    transition: background-color var(--vp-transition-fast);
}

.menu-toggle:hover {
    background-color: var(--vp-background-alt);
}

.menu-toggle-icon {
    display: flex;
    flex-direction: column;
    width: 24px;
    height: 18px;
    justify-content: space-between;
}

.menu-toggle-icon span {
    display: block;
    height: 2px;
    width: 100%;
    background-color: var(--vp-text-primary);
    border-radius: 1px;
    transition: all var(--vp-transition-fast);
}

.menu-toggle[aria-expanded="true"] .menu-toggle-icon span:nth-child(1) {
    transform: rotate(45deg) translate(6px, 6px);
}

.menu-toggle[aria-expanded="true"] .menu-toggle-icon span:nth-child(2) {
    opacity: 0;
}

.menu-toggle[aria-expanded="true"] .menu-toggle-icon span:nth-child(3) {
    transform: rotate(-45deg) translate(6px, -6px);
}

/* Header Actions */
.header-actions {
    display: flex;
    align-items: center;
    gap: var(--vp-space-2);
}

/* Search Toggle */
.search-toggle {
    position: relative;
}

.search-toggle-button {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 44px;
    height: 44px;
    background: none;
    border: none;
    border-radius: var(--vp-radius-md);
    color: var(--vp-text-secondary);
    cursor: pointer;
    transition: all var(--vp-transition-fast);
}

.search-toggle-button:hover {
    color: var(--vp-primary);
    background-color: var(--vp-background-alt);
}

.search-form-container {
    position: absolute;
    top: 100%;
    right: 0;
    width: 320px;
    background-color: var(--vp-background);
    border: 1px solid var(--vp-border);
    border-radius: var(--vp-radius-lg);
    box-shadow: var(--vp-shadow-lg);
    padding: var(--vp-space-4);
    opacity: 0;
    visibility: hidden;
    transform: translateY(-10px);
    transition: all var(--vp-transition-normal);
    z-index: 1001;
}

.search-form-container.active {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

.search-form {
    position: relative;
}

.search-form input[type="search"] {
    width: 100%;
    padding: var(--vp-space-3) var(--vp-space-4);
    border: 1px solid var(--vp-border);
    border-radius: var(--vp-radius-md);
    font-size: var(--vp-font-size-sm);
    background-color: var(--vp-background);
    transition: border-color var(--vp-transition-fast);
}

.search-form input[type="search"]:focus {
    outline: none;
    border-color: var(--vp-primary);
    box-shadow: 0 0 0 3px var(--vp-primary-light);
}

.search-form button {
    position: absolute;
    right: var(--vp-space-1);
    top: 50%;
    transform: translateY(-50%);
    background: none;
    border: none;
    padding: var(--vp-space-2);
    color: var(--vp-text-tertiary);
    cursor: pointer;
    border-radius: var(--vp-radius-sm);
    transition: color var(--vp-transition-fast);
}

.search-form button:hover {
    color: var(--vp-primary);
}

/* Social Navigation */
.social-navigation {
    display: flex;
    align-items: center;
}

.social-links-menu {
    display: flex;
    align-items: center;
    list-style: none;
    margin: 0;
    padding: 0;
    gap: var(--vp-space-1);
}

.social-links-menu a {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    color: var(--vp-text-tertiary);
    border-radius: var(--vp-radius-md);
    transition: all var(--vp-transition-fast);
}

.social-links-menu a:hover {
    color: var(--vp-primary);
    background-color: var(--vp-background-alt);
}

/* Responsive Design */
@media (max-width: 1024px) {
    .site-header .vp-container {
        padding-top: var(--vp-space-3);
        padding-bottom: var(--vp-space-3);
        min-height: 70px;
    }
    
    .primary-menu {
        gap: 0;
    }
    
    .primary-menu a {
        padding: var(--vp-space-2) var(--vp-space-3);
        font-size: var(--vp-font-size-sm);
    }
}

@media (max-width: 768px) {
    .menu-toggle {
        display: block;
    }
    
    .main-navigation {
        position: relative;
    }
    
    .primary-menu {
        position: absolute;
        top: 100%;
        left: 0;
        right: 0;
        flex-direction: column;
        background-color: var(--vp-background);
        border: 1px solid var(--vp-border);
        border-radius: var(--vp-radius-lg);
        box-shadow: var(--vp-shadow-lg);
        padding: var(--vp-space-2);
        margin-top: var(--vp-space-2);
        opacity: 0;
        visibility: hidden;
        transform: translateY(-10px);
        transition: all var(--vp-transition-normal);
        z-index: 1000;
    }
    
    .main-navigation.active .primary-menu {
        opacity: 1;
        visibility: visible;
        transform: translateY(0);
    }
    
    .primary-menu li {
        width: 100%;
    }
    
    .primary-menu a {
        width: 100%;
        padding: var(--vp-space-3) var(--vp-space-4);
        border-radius: var(--vp-radius-md);
    }
    
    .search-form-container {
        width: 280px;
    }
    
    .social-links-menu {
        gap: var(--vp-space-1);
    }
    
    .social-links-menu a {
        width: 36px;
        height: 36px;
    }
}

@media (max-width: 480px) {
    .site-header .vp-container {
        gap: var(--vp-space-2);
    }
    
    .site-title {
        font-size: var(--vp-font-size-xl);
    }
    
    .search-form-container {
        width: 260px;
        right: -20px;
    }
    
    .header-actions {
        gap: var(--vp-space-1);
    }
}
