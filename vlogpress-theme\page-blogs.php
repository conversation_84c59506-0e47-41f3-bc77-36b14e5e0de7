<?php
/**
 * Template for displaying all blog posts
 * 
 * Template Name: Blogs Page
 *
 * @package VlogPress
 * @since 1.0.0
 */

get_header(); ?>

<main id="main" class="site-main blogs-page" role="main">
    <div class="vp-container">

        <header class="page-header blogs-header">
            <h1 class="page-title">
                <svg class="page-icon" width="32" height="32" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z" stroke="currentColor" stroke-width="2" fill="none"/>
                    <polyline points="14,2 14,8 20,8" stroke="currentColor" stroke-width="2" fill="none"/>
                    <line x1="16" y1="13" x2="8" y2="13" stroke="currentColor" stroke-width="2"/>
                    <line x1="16" y1="17" x2="8" y2="17" stroke="currentColor" stroke-width="2"/>
                    <polyline points="10,9 9,9 8,9" stroke="currentColor" stroke-width="2"/>
                </svg>
                <?php _e( 'Blog Articles', 'vlogpress' ); ?>
            </h1>
            <p class="page-description">
                <?php _e( 'Read our latest articles, insights, and written content.', 'vlogpress' ); ?>
            </p>
        </header>

        <?php
        // Custom query for blog posts only
        $paged = ( get_query_var( 'paged' ) ) ? get_query_var( 'paged' ) : 1;
        
        $blogs_query = new WP_Query( array(
            'post_type'      => 'post',
            'post_status'    => 'publish',
            'posts_per_page' => get_option( 'posts_per_page' ),
            'paged'          => $paged,
        ) );

        if ( $blogs_query->have_posts() ) : ?>

            <div class="blogs-filters">
                <div class="filter-buttons">
                    <button class="filter-btn active" data-filter="all"><?php _e( 'All Articles', 'vlogpress' ); ?></button>
                    <?php
                    // Get categories used by blog posts
                    $blog_categories = get_terms( array(
                        'taxonomy' => 'category',
                        'hide_empty' => true,
                        'object_ids' => wp_list_pluck( $blogs_query->posts, 'ID' )
                    ) );
                    
                    if ( ! empty( $blog_categories ) && ! is_wp_error( $blog_categories ) ) :
                        foreach ( $blog_categories as $category ) : ?>
                            <button class="filter-btn" data-filter="<?php echo esc_attr( $category->slug ); ?>">
                                <?php echo esc_html( $category->name ); ?>
                            </button>
                        <?php endforeach;
                    endif; ?>
                </div>
            </div>

            <div class="vp-content-grid blogs-grid">
                <?php while ( $blogs_query->have_posts() ) : $blogs_query->the_post(); ?>

                    <article id="post-<?php the_ID(); ?>" <?php post_class( 'vp-content-card blog-card' ); ?> data-categories="<?php echo esc_attr( implode( ' ', wp_get_post_categories( get_the_ID(), array( 'fields' => 'slugs' ) ) ) ); ?>">

                        <?php if ( has_post_thumbnail() ) : ?>
                            <div class="vp-card-thumbnail blog-thumbnail">
                                <a href="<?php the_permalink(); ?>" aria-hidden="true" tabindex="-1">
                                    <?php the_post_thumbnail( 'medium_large', array( 'alt' => the_title_attribute( array( 'echo' => false ) ) ) ); ?>
                                </a>
                            </div>
                        <?php endif; ?>

                        <div class="vp-card-content">
                            <header class="vp-card-header">
                                <?php the_title( '<h2 class="vp-card-title"><a href="' . esc_url( get_permalink() ) . '" rel="bookmark">', '</a></h2>' ); ?>

                                <div class="vp-card-meta">
                                    <span class="vp-content-type vp-type-blog">
                                        <?php _e( 'Article', 'vlogpress' ); ?>
                                    </span>

                                    <time class="vp-published-date" datetime="<?php echo esc_attr( get_the_date( 'c' ) ); ?>">
                                        <?php echo esc_html( get_the_date() ); ?>
                                    </time>

                                    <span class="vp-reading-time">
                                        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                            <circle cx="12" cy="12" r="10" stroke="currentColor" stroke-width="2"/>
                                            <polyline points="12,6 12,12 16,14" stroke="currentColor" stroke-width="2"/>
                                        </svg>
                                        <?php echo vp_get_reading_time(); ?>
                                    </span>

                                    <span class="vp-author">
                                        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                            <path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2" stroke="currentColor" stroke-width="2"/>
                                            <circle cx="12" cy="7" r="4" stroke="currentColor" stroke-width="2"/>
                                        </svg>
                                        <?php _e( 'By', 'vlogpress' ); ?> <?php the_author(); ?>
                                    </span>
                                </div>
                            </header>

                            <div class="vp-card-excerpt">
                                <?php the_excerpt(); ?>
                            </div>

                            <footer class="vp-card-footer">
                                <a href="<?php the_permalink(); ?>" class="vp-read-more vp-read-article">
                                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                        <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z" stroke="currentColor" stroke-width="2" fill="none"/>
                                        <polyline points="14,2 14,8 20,8" stroke="currentColor" stroke-width="2" fill="none"/>
                                    </svg>
                                    <?php _e( 'Read Article', 'vlogpress' ); ?>
                                    <span class="vp-sr-only"><?php printf( __( 'about %s', 'vlogpress' ), get_the_title() ); ?></span>
                                </a>

                                <?php if ( has_category() ) : ?>
                                    <div class="vp-card-terms">
                                        <?php
                                        $categories = get_the_category();
                                        if ( $categories ) {
                                            echo '<span class="vp-category">' . esc_html( $categories[0]->name ) . '</span>';
                                        }
                                        ?>
                                    </div>
                                <?php endif; ?>
                            </footer>
                        </div>
                    </article>

                <?php endwhile; ?>
            </div>

            <?php
            // Custom pagination for blogs
            $big = 999999999; // need an unlikely integer
            echo paginate_links( array(
                'base'      => str_replace( $big, '%#%', esc_url( get_pagenum_link( $big ) ) ),
                'format'    => '?paged=%#%',
                'current'   => max( 1, get_query_var( 'paged' ) ),
                'total'     => $blogs_query->max_num_pages,
                'mid_size'  => 2,
                'prev_text' => __( '&larr; Previous', 'vlogpress' ),
                'next_text' => __( 'Next &rarr;', 'vlogpress' ),
                'type'      => 'list',
                'class'     => 'vp-pagination blogs-pagination',
            ) );
            ?>

        <?php else : ?>

            <section class="vp-no-results blogs-no-results">
                <header class="page-header">
                    <h2 class="page-title"><?php _e( 'No articles found', 'vlogpress' ); ?></h2>
                </header>

                <div class="page-content">
                    <p><?php _e( 'No blog articles have been published yet. Check back soon for new content!', 'vlogpress' ); ?></p>
                    
                    <?php if ( current_user_can( 'publish_posts' ) ) : ?>
                        <p>
                            <a href="<?php echo esc_url( admin_url( 'post-new.php' ) ); ?>" class="vp-button">
                                <?php _e( 'Write Your First Article', 'vlogpress' ); ?>
                            </a>
                        </p>
                    <?php endif; ?>
                </div>
            </section>

        <?php endif; ?>

        <?php wp_reset_postdata(); ?>

    </div>
</main>

<?php
get_sidebar();
get_footer();
