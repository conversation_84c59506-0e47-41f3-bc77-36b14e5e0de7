/* ==========================================================================
   WordPress Block Editor Styles
   ========================================================================== */

/* Import main theme styles */
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap');

/* Editor Body */
.wp-block {
    max-width: 800px;
}

.wp-block[data-align="wide"] {
    max-width: 1280px;
}

.wp-block[data-align="full"] {
    max-width: none;
}

/* Typography */
.editor-styles-wrapper {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Sego<PERSON> UI', <PERSON><PERSON>, 'Helvetica Neue', Arial, sans-serif;
    font-size: 1rem;
    line-height: 1.6;
    color: #4a5568;
}

.editor-styles-wrapper h1,
.editor-styles-wrapper h2,
.editor-styles-wrapper h3,
.editor-styles-wrapper h4,
.editor-styles-wrapper h5,
.editor-styles-wrapper h6 {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
    font-weight: 600;
    color: #1a202c;
    line-height: 1.25;
    margin-top: 0;
    margin-bottom: 1rem;
}

.editor-styles-wrapper h1 {
    font-size: 2.25rem;
    font-weight: 700;
    line-height: 1.1;
    letter-spacing: -0.05em;
}

.editor-styles-wrapper h2 {
    font-size: 1.875rem;
    letter-spacing: -0.025em;
}

.editor-styles-wrapper h3 {
    font-size: 1.5rem;
}

.editor-styles-wrapper h4 {
    font-size: 1.25rem;
    font-weight: 500;
}

.editor-styles-wrapper h5 {
    font-size: 1.125rem;
    font-weight: 500;
}

.editor-styles-wrapper h6 {
    font-size: 1rem;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

.editor-styles-wrapper p {
    margin-bottom: 1rem;
    line-height: 1.625;
}

.editor-styles-wrapper a {
    color: #0066cc;
    text-decoration: none;
    transition: color 0.15s ease;
}

.editor-styles-wrapper a:hover {
    color: #004499;
}

/* Block Styles */

/* Paragraph */
.wp-block-paragraph.has-drop-cap:not(:focus)::first-letter {
    font-family: 'Inter', serif;
    font-weight: 700;
    color: #0066cc;
    background: linear-gradient(135deg, #e6f2ff 0%, #f7fafc 100%);
    border-radius: 0.5rem;
    padding: 0.5rem;
    margin: 0.125rem 0.5rem 0 0;
}

/* Headings */
.wp-block-heading {
    margin-top: 2rem;
    margin-bottom: 1rem;
}

.wp-block-heading:first-child {
    margin-top: 0;
}

/* Lists */
.wp-block-list {
    padding-left: 1.5rem;
    margin-bottom: 1rem;
}

.wp-block-list li {
    margin-bottom: 0.5rem;
    line-height: 1.625;
}

/* Quote */
.wp-block-quote {
    border-left: 4px solid #0066cc;
    padding-left: 1.5rem;
    margin: 2rem 0;
    background-color: #f7fafc;
    padding: 1.5rem;
    border-radius: 0 0.75rem 0.75rem 0;
}

.wp-block-quote p {
    font-size: 1.125rem;
    font-style: italic;
    color: #1a202c;
    margin-bottom: 0.5rem;
}

.wp-block-quote cite {
    font-size: 0.875rem;
    color: #718096;
    font-style: normal;
}

/* Pullquote */
.wp-block-pullquote {
    border-top: 4px solid #0066cc;
    border-bottom: 4px solid #0066cc;
    padding: 2rem 0;
    text-align: center;
    margin: 2rem 0;
}

.wp-block-pullquote blockquote {
    margin: 0;
    border: none;
    padding: 0;
    background: none;
}

.wp-block-pullquote p {
    font-size: 1.5rem;
    font-weight: 500;
    line-height: 1.4;
    color: #1a202c;
}

.wp-block-pullquote cite {
    font-size: 1rem;
    color: #718096;
    text-transform: uppercase;
    letter-spacing: 0.05em;
    font-weight: 500;
}

/* Code */
.wp-block-code {
    background-color: #edf2f7;
    border-radius: 0.5rem;
    padding: 1rem;
    font-family: 'SF Mono', Monaco, 'Cascadia Code', 'Roboto Mono', Consolas, 'Courier New', monospace;
    font-size: 0.875rem;
    overflow-x: auto;
}

.wp-block-code code {
    background: none;
    padding: 0;
    border-radius: 0;
    color: #1a202c;
}

/* Preformatted */
.wp-block-preformatted {
    background-color: #edf2f7;
    border-radius: 0.5rem;
    padding: 1rem;
    font-family: 'SF Mono', Monaco, 'Cascadia Code', 'Roboto Mono', Consolas, 'Courier New', monospace;
    font-size: 0.875rem;
    overflow-x: auto;
    white-space: pre-wrap;
}

/* Buttons */
.wp-block-button .wp-block-button__link {
    background-color: #0066cc;
    color: #ffffff;
    border-radius: 0.5rem;
    padding: 0.75rem 1.5rem;
    font-weight: 500;
    text-decoration: none;
    display: inline-block;
    transition: all 0.15s ease;
    border: 2px solid #0066cc;
}

.wp-block-button .wp-block-button__link:hover {
    background-color: #004499;
    border-color: #004499;
    transform: translateY(-1px);
}

.wp-block-button.is-style-outline .wp-block-button__link {
    background-color: transparent;
    color: #0066cc;
    border: 2px solid #0066cc;
}

.wp-block-button.is-style-outline .wp-block-button__link:hover {
    background-color: #0066cc;
    color: #ffffff;
}

/* Separator */
.wp-block-separator {
    border: none;
    border-top: 1px solid #e2e8f0;
    margin: 2rem 0;
}

.wp-block-separator.is-style-wide {
    border-top-width: 2px;
}

.wp-block-separator.is-style-dots {
    border: none;
    text-align: center;
    line-height: 1;
    height: auto;
}

.wp-block-separator.is-style-dots::before {
    content: '···';
    color: #718096;
    font-size: 1.5rem;
    letter-spacing: 1rem;
    padding-left: 1rem;
}

/* Spacer */
.wp-block-spacer {
    clear: both;
}

/* Image */
.wp-block-image {
    margin-bottom: 1rem;
}

.wp-block-image img {
    height: auto;
    max-width: 100%;
    border-radius: 0.5rem;
}

.wp-block-image figcaption {
    text-align: center;
    font-size: 0.875rem;
    color: #718096;
    margin-top: 0.5rem;
    font-style: italic;
}

/* Gallery */
.wp-block-gallery {
    margin-bottom: 1rem;
}

.wp-block-gallery .blocks-gallery-item img {
    border-radius: 0.5rem;
}

/* Cover */
.wp-block-cover {
    border-radius: 0.75rem;
    overflow: hidden;
}

.wp-block-cover .wp-block-cover__inner-container {
    padding: 2rem;
}

/* Group */
.wp-block-group {
    margin-bottom: 1rem;
}

.wp-block-group.has-background {
    padding: 1.5rem;
    border-radius: 0.75rem;
}

/* Columns */
.wp-block-columns {
    margin-bottom: 1rem;
}

.wp-block-column {
    margin-bottom: 0;
}

/* Media & Text */
.wp-block-media-text {
    margin-bottom: 1rem;
    border-radius: 0.75rem;
    overflow: hidden;
}

.wp-block-media-text.has-background {
    padding: 1.5rem;
}

/* Table */
.wp-block-table {
    margin-bottom: 1rem;
}

.wp-block-table table {
    border-collapse: collapse;
    width: 100%;
}

.wp-block-table th,
.wp-block-table td {
    padding: 0.75rem;
    border: 1px solid #e2e8f0;
    text-align: left;
}

.wp-block-table th {
    background-color: #f7fafc;
    font-weight: 600;
    color: #1a202c;
}

.wp-block-table.is-style-stripes tbody tr:nth-child(odd) {
    background-color: #f7fafc;
}

/* Verse */
.wp-block-verse {
    background-color: #f7fafc;
    border-left: 4px solid #0066cc;
    padding: 1rem 1.5rem;
    font-family: inherit;
    border-radius: 0 0.5rem 0.5rem 0;
    margin: 1rem 0;
}

/* Latest Posts */
.wp-block-latest-posts {
    list-style: none;
    padding: 0;
}

.wp-block-latest-posts li {
    margin-bottom: 1rem;
    padding: 1rem;
    background-color: #f7fafc;
    border-radius: 0.5rem;
    border-left: 3px solid #0066cc;
}

.wp-block-latest-posts a {
    font-weight: 600;
    color: #1a202c;
}

.wp-block-latest-posts a:hover {
    color: #0066cc;
}

.wp-block-latest-posts__post-date {
    font-size: 0.875rem;
    color: #718096;
    margin-top: 0.25rem;
}

/* Responsive Design */
@media (max-width: 768px) {
    .editor-styles-wrapper h1 {
        font-size: 1.875rem;
    }
    
    .editor-styles-wrapper h2 {
        font-size: 1.5rem;
    }
    
    .wp-block-pullquote p {
        font-size: 1.25rem;
    }
    
    .wp-block-cover .wp-block-cover__inner-container {
        padding: 1rem;
    }
}
