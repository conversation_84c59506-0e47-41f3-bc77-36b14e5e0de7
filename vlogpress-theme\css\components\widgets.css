/* ==========================================================================
   WordPress Standard Widget Styles
   ========================================================================== */

/* Widget Area Container */
.widget-area {
    margin-bottom: var(--vp-space-8);
}

.sidebar-inner {
    padding: var(--vp-space-6);
    background-color: var(--vp-background-alt);
    border-radius: var(--vp-radius-xl);
    border: 1px solid var(--vp-border-light);
}

/* Individual Widget Styling */
.widget {
    margin-bottom: var(--vp-space-8);
    padding: var(--vp-space-6);
    background-color: var(--vp-background);
    border-radius: var(--vp-radius-lg);
    border: 1px solid var(--vp-border-light);
    box-shadow: var(--vp-shadow-sm);
    transition: all var(--vp-transition-normal);
}

.widget:hover {
    box-shadow: var(--vp-shadow-md);
    transform: translateY(-1px);
}

.widget:last-child {
    margin-bottom: 0;
}

/* Widget Titles */
.widget-title {
    margin: 0 0 var(--vp-space-4) 0;
    font-size: var(--vp-font-size-lg);
    font-weight: var(--vp-font-weight-semibold);
    color: var(--vp-text-primary);
    line-height: var(--vp-line-height-tight);
    position: relative;
    padding-bottom: var(--vp-space-2);
}

.widget-title::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 30px;
    height: 2px;
    background-color: var(--vp-primary);
    border-radius: 1px;
}

/* Widget Content */
.widget p {
    margin-bottom: var(--vp-space-3);
    line-height: var(--vp-line-height-relaxed);
    color: var(--vp-text-secondary);
}

.widget p:last-child {
    margin-bottom: 0;
}

/* Widget Lists */
.widget ul {
    list-style: none;
    padding: 0;
    margin: 0;
}

.widget li {
    margin-bottom: var(--vp-space-2);
    padding: var(--vp-space-2) 0;
    border-bottom: 1px solid var(--vp-border-light);
    transition: all var(--vp-transition-fast);
}

.widget li:last-child {
    margin-bottom: 0;
    border-bottom: none;
}

.widget li:hover {
    padding-left: var(--vp-space-2);
    background-color: var(--vp-background-alt);
    border-radius: var(--vp-radius-sm);
}

.widget a {
    color: var(--vp-text-secondary);
    text-decoration: none;
    transition: color var(--vp-transition-fast);
    display: block;
}

.widget a:hover {
    color: var(--vp-primary);
}

/* WordPress Core Widgets */

/* Search Widget */
.widget_search .search-form {
    position: relative;
}

.widget_search input[type="search"] {
    width: 100%;
    padding: var(--vp-space-3) var(--vp-space-12) var(--vp-space-3) var(--vp-space-4);
    border: 1px solid var(--vp-border);
    border-radius: var(--vp-radius-lg);
    background-color: var(--vp-background-alt);
    font-size: var(--vp-font-size-sm);
    transition: all var(--vp-transition-fast);
}

.widget_search input[type="search"]:focus {
    outline: none;
    border-color: var(--vp-primary);
    box-shadow: 0 0 0 3px var(--vp-primary-light);
    background-color: var(--vp-background);
}

.widget_search button {
    position: absolute;
    right: var(--vp-space-2);
    top: 50%;
    transform: translateY(-50%);
    background: none;
    border: none;
    padding: var(--vp-space-2);
    color: var(--vp-text-tertiary);
    cursor: pointer;
    border-radius: var(--vp-radius-sm);
    transition: all var(--vp-transition-fast);
}

.widget_search button:hover {
    color: var(--vp-primary);
    background-color: var(--vp-primary-light);
}

/* Recent Posts Widget */
.widget_recent_entries ul {
    list-style: none;
}

.widget_recent_entries li {
    display: flex;
    align-items: flex-start;
    gap: var(--vp-space-3);
    padding: var(--vp-space-3);
    border-radius: var(--vp-radius-md);
}

.widget_recent_entries a {
    font-weight: var(--vp-font-weight-medium);
    line-height: var(--vp-line-height-tight);
}

.widget_recent_entries .post-date {
    font-size: var(--vp-font-size-xs);
    color: var(--vp-text-tertiary);
    margin-top: var(--vp-space-1);
}

/* Categories Widget */
.widget_categories ul,
.widget_archive ul {
    list-style: none;
}

.widget_categories li,
.widget_archive li {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.widget_categories .count,
.widget_archive .count {
    background-color: var(--vp-primary-light);
    color: var(--vp-primary);
    padding: var(--vp-space-1) var(--vp-space-2);
    border-radius: var(--vp-radius-full);
    font-size: var(--vp-font-size-xs);
    font-weight: var(--vp-font-weight-medium);
    min-width: 24px;
    text-align: center;
}

/* Tag Cloud Widget */
.widget_tag_cloud .tagcloud {
    display: flex;
    flex-wrap: wrap;
    gap: var(--vp-space-2);
    margin-top: var(--vp-space-2);
}

.widget_tag_cloud .tagcloud a {
    display: inline-block;
    padding: var(--vp-space-1) var(--vp-space-3);
    background-color: var(--vp-background-alt);
    color: var(--vp-text-secondary);
    border-radius: var(--vp-radius-full);
    font-size: var(--vp-font-size-sm) !important;
    font-weight: var(--vp-font-weight-medium);
    text-decoration: none;
    transition: all var(--vp-transition-fast);
    border: 1px solid var(--vp-border-light);
}

.widget_tag_cloud .tagcloud a:hover {
    background-color: var(--vp-primary);
    color: var(--vp-text-inverse);
    border-color: var(--vp-primary);
    transform: translateY(-1px);
}

/* Calendar Widget */
.widget_calendar table {
    width: 100%;
    border-collapse: collapse;
    margin-top: var(--vp-space-2);
}

.widget_calendar th,
.widget_calendar td {
    padding: var(--vp-space-2);
    text-align: center;
    border: 1px solid var(--vp-border-light);
}

.widget_calendar th {
    background-color: var(--vp-background-alt);
    font-weight: var(--vp-font-weight-semibold);
    color: var(--vp-text-primary);
}

.widget_calendar td {
    background-color: var(--vp-background);
}

.widget_calendar td a {
    color: var(--vp-primary);
    font-weight: var(--vp-font-weight-medium);
    text-decoration: none;
    display: block;
    padding: var(--vp-space-1);
    border-radius: var(--vp-radius-sm);
    transition: all var(--vp-transition-fast);
}

.widget_calendar td a:hover {
    background-color: var(--vp-primary);
    color: var(--vp-text-inverse);
}

/* Text Widget */
.widget_text .textwidget {
    line-height: var(--vp-line-height-relaxed);
}

.widget_text .textwidget h1,
.widget_text .textwidget h2,
.widget_text .textwidget h3,
.widget_text .textwidget h4,
.widget_text .textwidget h5,
.widget_text .textwidget h6 {
    margin-top: 0;
    margin-bottom: var(--vp-space-3);
}

/* RSS Widget */
.widget_rss ul {
    list-style: none;
}

.widget_rss li {
    padding: var(--vp-space-3);
    border-radius: var(--vp-radius-md);
}

.widget_rss .rsswidget {
    font-weight: var(--vp-font-weight-medium);
    color: var(--vp-text-primary);
}

.widget_rss .rss-date {
    font-size: var(--vp-font-size-xs);
    color: var(--vp-text-tertiary);
    margin-top: var(--vp-space-1);
}

.widget_rss cite {
    font-style: normal;
    font-size: var(--vp-font-size-sm);
    color: var(--vp-text-secondary);
    margin-top: var(--vp-space-2);
    display: block;
}

/* Custom Widgets */
.widget_media_image img {
    width: 100%;
    height: auto;
    border-radius: var(--vp-radius-md);
}

.widget_media_video video {
    width: 100%;
    height: auto;
    border-radius: var(--vp-radius-md);
}

/* Footer Widgets */
.footer-widget-area .widget {
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.1);
    padding: var(--vp-space-6);
}

.footer-widget-area .widget:hover {
    background: rgba(255, 255, 255, 0.08);
    transform: none;
}

.footer-widget-area .widget-title {
    color: var(--vp-text-inverse);
}

.footer-widget-area .widget-title::after {
    background-color: var(--vp-primary);
}

.footer-widget-area .widget p,
.footer-widget-area .widget a {
    color: rgba(255, 255, 255, 0.8);
}

.footer-widget-area .widget a:hover {
    color: var(--vp-text-inverse);
}

/* Responsive Design */
@media (max-width: 768px) {
    .sidebar-inner {
        padding: var(--vp-space-4);
    }
    
    .widget {
        padding: var(--vp-space-4);
        margin-bottom: var(--vp-space-6);
    }
    
    .widget-title {
        font-size: var(--vp-font-size-base);
    }
}

@media (max-width: 480px) {
    .widget_tag_cloud .tagcloud {
        gap: var(--vp-space-1);
    }
    
    .widget_tag_cloud .tagcloud a {
        padding: var(--vp-space-1) var(--vp-space-2);
        font-size: var(--vp-font-size-xs) !important;
    }
}
