{"version": 2, "settings": {"appearanceTools": true, "useRootPaddingAwareAlignments": true, "color": {"custom": true, "customDuotone": true, "customGradient": true, "defaultDuotones": false, "defaultGradients": false, "defaultPalette": false, "duotone": [{"colors": ["#0066cc", "#ffffff"], "slug": "primary-white", "name": "Primary and White"}, {"colors": ["#1a202c", "#ffffff"], "slug": "dark-white", "name": "Dark and White"}], "gradients": [{"gradient": "linear-gradient(135deg, #0066cc 0%, #004499 100%)", "name": "Primary Gradient", "slug": "primary-gradient"}, {"gradient": "linear-gradient(135deg, #e6f2ff 0%, #f7fafc 100%)", "name": "Light Gradient", "slug": "light-gradient"}], "palette": [{"color": "#0066cc", "name": "Primary", "slug": "primary"}, {"color": "#004499", "name": "Primary Dark", "slug": "primary-dark"}, {"color": "#e6f2ff", "name": "Primary Light", "slug": "primary-light"}, {"color": "#1a202c", "name": "Text Primary", "slug": "text-primary"}, {"color": "#4a5568", "name": "Text Secondary", "slug": "text-secondary"}, {"color": "#718096", "name": "Text Tertiary", "slug": "text-tertiary"}, {"color": "#ffffff", "name": "Background", "slug": "background"}, {"color": "#f7fafc", "name": "Background Alt", "slug": "background-alt"}, {"color": "#edf2f7", "name": "Background Subtle", "slug": "background-subtle"}, {"color": "#38a169", "name": "Success", "slug": "success"}, {"color": "#d69e2e", "name": "Warning", "slug": "warning"}, {"color": "#e53e3e", "name": "Error", "slug": "error"}]}, "typography": {"customFontSize": true, "dropCap": true, "fontStyle": true, "fontWeight": true, "letterSpacing": true, "lineHeight": true, "textDecoration": true, "textTransform": true, "fontFamilies": [{"fontFamily": "'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif", "name": "Primary Font", "slug": "primary"}, {"fontFamily": "'SF Mono', Monaco, 'Cascadia Code', 'Roboto Mono', Consolas, 'Courier New', monospace", "name": "Monospace", "slug": "monospace"}], "fontSizes": [{"name": "Extra Small", "size": "0.75rem", "slug": "x-small"}, {"name": "Small", "size": "0.875rem", "slug": "small"}, {"name": "Medium", "size": "1rem", "slug": "medium"}, {"name": "Large", "size": "1.125rem", "slug": "large"}, {"name": "Extra Large", "size": "1.25rem", "slug": "x-large"}, {"name": "XX Large", "size": "1.5rem", "slug": "xx-large"}, {"name": "XXX Large", "size": "1.875rem", "slug": "xxx-large"}, {"name": "<PERSON>ge", "size": "2.25rem", "slug": "huge"}]}, "spacing": {"blockGap": true, "margin": true, "padding": true, "units": ["px", "em", "rem", "vh", "vw", "%"], "spacingScale": {"operator": "*", "increment": 1.5, "steps": 7, "mediumStep": 1.5, "unit": "rem"}, "spacingSizes": [{"name": "1", "size": "0.25rem", "slug": "30"}, {"name": "2", "size": "0.5rem", "slug": "40"}, {"name": "3", "size": "0.75rem", "slug": "50"}, {"name": "4", "size": "1rem", "slug": "60"}, {"name": "5", "size": "1.25rem", "slug": "70"}, {"name": "6", "size": "1.5rem", "slug": "80"}]}, "border": {"color": true, "radius": true, "style": true, "width": true}, "layout": {"contentSize": "800px", "wideSize": "1280px"}}, "styles": {"color": {"background": "var(--wp--preset--color--background)", "text": "var(--wp--preset--color--text-secondary)"}, "typography": {"fontFamily": "var(--wp--preset--font-family--primary)", "fontSize": "var(--wp--preset--font-size--medium)", "lineHeight": "1.6"}, "spacing": {"blockGap": "1.5rem"}, "elements": {"link": {"color": {"text": "var(--wp--preset--color--primary)"}, ":hover": {"color": {"text": "var(--wp--preset--color--primary-dark)"}}}, "h1": {"typography": {"fontSize": "var(--wp--preset--font-size--huge)", "fontWeight": "700", "lineHeight": "1.2", "letterSpacing": "-0.05em"}, "color": {"text": "var(--wp--preset--color--text-primary)"}}, "h2": {"typography": {"fontSize": "var(--wp--preset--font-size--xxx-large)", "fontWeight": "600", "lineHeight": "1.25", "letterSpacing": "-0.025em"}, "color": {"text": "var(--wp--preset--color--text-primary)"}}, "h3": {"typography": {"fontSize": "var(--wp--preset--font-size--xx-large)", "fontWeight": "600", "lineHeight": "1.3"}, "color": {"text": "var(--wp--preset--color--text-primary)"}}, "h4": {"typography": {"fontSize": "var(--wp--preset--font-size--x-large)", "fontWeight": "500", "lineHeight": "1.4"}, "color": {"text": "var(--wp--preset--color--text-primary)"}}, "h5": {"typography": {"fontSize": "var(--wp--preset--font-size--large)", "fontWeight": "500"}, "color": {"text": "var(--wp--preset--color--text-primary)"}}, "h6": {"typography": {"fontSize": "var(--wp--preset--font-size--medium)", "fontWeight": "500", "textTransform": "uppercase", "letterSpacing": "0.05em"}, "color": {"text": "var(--wp--preset--color--text-primary)"}}}, "blocks": {"core/button": {"border": {"radius": "0.5rem"}, "color": {"background": "var(--wp--preset--color--primary)", "text": "var(--wp--preset--color--background)"}, "typography": {"fontWeight": "500"}}, "core/pullquote": {"border": {"color": "var(--wp--preset--color--primary)", "style": "solid", "width": "1px 0"}}, "core/quote": {"border": {"color": "var(--wp--preset--color--primary)", "style": "solid", "width": "0 0 0 4px"}, "spacing": {"padding": {"left": "1.5rem"}}}, "core/separator": {"color": {"text": "var(--wp--preset--color--text-tertiary)"}}}}, "templateParts": [{"name": "header", "title": "Header", "area": "header"}, {"name": "footer", "title": "Footer", "area": "footer"}]}