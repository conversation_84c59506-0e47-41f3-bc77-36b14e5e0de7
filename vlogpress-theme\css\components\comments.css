/* ==========================================================================
   WordPress Comments Styling
   ========================================================================== */

/* Comments Area */
.comments-area {
    margin-top: var(--vp-space-12);
    padding-top: var(--vp-space-8);
    border-top: 1px solid var(--vp-border-light);
}

.comments-title {
    margin-bottom: var(--vp-space-6);
    font-size: var(--vp-font-size-2xl);
    font-weight: var(--vp-font-weight-semibold);
    color: var(--vp-text-primary);
}

/* Comments Navigation */
.comment-navigation {
    margin-bottom: var(--vp-space-6);
    padding: var(--vp-space-4);
    background-color: var(--vp-background-alt);
    border-radius: var(--vp-radius-lg);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.comment-navigation .nav-previous,
.comment-navigation .nav-next {
    flex: 1;
}

.comment-navigation .nav-next {
    text-align: right;
}

.comment-navigation a {
    color: var(--vp-primary);
    text-decoration: none;
    font-weight: var(--vp-font-weight-medium);
    padding: var(--vp-space-2) var(--vp-space-4);
    border-radius: var(--vp-radius-md);
    transition: all var(--vp-transition-fast);
}

.comment-navigation a:hover {
    background-color: var(--vp-primary-light);
    color: var(--vp-primary-dark);
}

/* Comment List */
.comment-list {
    list-style: none;
    margin: 0;
    padding: 0;
}

.comment-list .comment {
    margin-bottom: var(--vp-space-6);
    padding: var(--vp-space-6);
    background-color: var(--vp-background);
    border: 1px solid var(--vp-border-light);
    border-radius: var(--vp-radius-lg);
    box-shadow: var(--vp-shadow-sm);
    transition: all var(--vp-transition-normal);
}

.comment-list .comment:hover {
    box-shadow: var(--vp-shadow-md);
    transform: translateY(-1px);
}

/* Comment Body */
.comment-body {
    position: relative;
}

.comment-author {
    display: flex;
    align-items: flex-start;
    gap: var(--vp-space-3);
    margin-bottom: var(--vp-space-4);
}

.comment-avatar {
    flex-shrink: 0;
}

.comment-avatar img {
    border-radius: var(--vp-radius-full);
    border: 2px solid var(--vp-border-light);
    transition: border-color var(--vp-transition-fast);
}

.comment:hover .comment-avatar img {
    border-color: var(--vp-primary-light);
}

.comment-metadata {
    flex: 1;
}

.comment-metadata .fn {
    font-size: var(--vp-font-size-lg);
    font-weight: var(--vp-font-weight-semibold);
    color: var(--vp-text-primary);
    font-style: normal;
    margin-bottom: var(--vp-space-1);
    display: block;
}

.comment-metadata .fn a {
    color: var(--vp-text-primary);
    text-decoration: none;
    transition: color var(--vp-transition-fast);
}

.comment-metadata .fn a:hover {
    color: var(--vp-primary);
}

.comment-meta {
    font-size: var(--vp-font-size-sm);
    color: var(--vp-text-tertiary);
}

.comment-meta a {
    color: var(--vp-text-tertiary);
    text-decoration: none;
    transition: color var(--vp-transition-fast);
}

.comment-meta a:hover {
    color: var(--vp-primary);
}

/* Comment Content */
.comment-content {
    margin-bottom: var(--vp-space-4);
    line-height: var(--vp-line-height-relaxed);
    color: var(--vp-text-secondary);
}

.comment-content p {
    margin-bottom: var(--vp-space-3);
}

.comment-content p:last-child {
    margin-bottom: 0;
}

/* Comment Awaiting Moderation */
.comment-awaiting-moderation {
    display: inline-block;
    padding: var(--vp-space-2) var(--vp-space-3);
    background-color: var(--vp-warning-light);
    color: var(--vp-warning);
    border-radius: var(--vp-radius-md);
    font-size: var(--vp-font-size-sm);
    font-weight: var(--vp-font-weight-medium);
    margin-bottom: var(--vp-space-3);
}

/* Reply Link */
.reply {
    text-align: right;
}

.comment-reply-link {
    display: inline-flex;
    align-items: center;
    gap: var(--vp-space-1);
    padding: var(--vp-space-2) var(--vp-space-4);
    background-color: var(--vp-background-alt);
    color: var(--vp-primary);
    text-decoration: none;
    border-radius: var(--vp-radius-md);
    font-size: var(--vp-font-size-sm);
    font-weight: var(--vp-font-weight-medium);
    transition: all var(--vp-transition-fast);
    border: 1px solid var(--vp-border-light);
}

.comment-reply-link:hover {
    background-color: var(--vp-primary);
    color: var(--vp-text-inverse);
    border-color: var(--vp-primary);
    transform: translateY(-1px);
}

/* Nested Comments */
.comment-list .children {
    list-style: none;
    margin: var(--vp-space-6) 0 0 var(--vp-space-8);
    padding: 0;
}

.comment-list .children .comment {
    border-left: 3px solid var(--vp-primary-light);
    background-color: var(--vp-background-alt);
}

/* Comment Form */
.comment-respond {
    margin-top: var(--vp-space-8);
    padding: var(--vp-space-8);
    background-color: var(--vp-background-alt);
    border-radius: var(--vp-radius-xl);
    border: 1px solid var(--vp-border-light);
}

.comment-reply-title {
    margin-bottom: var(--vp-space-6);
    font-size: var(--vp-font-size-2xl);
    font-weight: var(--vp-font-weight-semibold);
    color: var(--vp-text-primary);
}

.comment-reply-title small {
    font-size: var(--vp-font-size-sm);
    font-weight: var(--vp-font-weight-normal);
}

.comment-reply-title small a {
    color: var(--vp-text-tertiary);
    text-decoration: none;
    transition: color var(--vp-transition-fast);
}

.comment-reply-title small a:hover {
    color: var(--vp-primary);
}

/* Comment Form Fields */
.comment-form p {
    margin-bottom: var(--vp-space-4);
}

.comment-form label {
    display: block;
    margin-bottom: var(--vp-space-2);
    font-weight: var(--vp-font-weight-medium);
    color: var(--vp-text-primary);
}

.comment-form .required {
    color: var(--vp-error);
}

.comment-form input[type="text"],
.comment-form input[type="email"],
.comment-form input[type="url"],
.comment-form textarea {
    width: 100%;
    padding: var(--vp-space-3) var(--vp-space-4);
    border: 1px solid var(--vp-border);
    border-radius: var(--vp-radius-lg);
    background-color: var(--vp-background);
    font-family: var(--vp-font-family-primary);
    font-size: var(--vp-font-size-sm);
    line-height: var(--vp-line-height-normal);
    transition: all var(--vp-transition-fast);
}

.comment-form input:focus,
.comment-form textarea:focus {
    outline: none;
    border-color: var(--vp-primary);
    box-shadow: 0 0 0 3px var(--vp-primary-light);
}

.comment-form textarea {
    resize: vertical;
    min-height: 120px;
}

/* Comment Form Notes */
.comment-notes,
.logged-in-as,
.must-log-in {
    font-size: var(--vp-font-size-sm);
    color: var(--vp-text-secondary);
    margin-bottom: var(--vp-space-4);
    padding: var(--vp-space-3) var(--vp-space-4);
    background-color: var(--vp-info-light);
    border-radius: var(--vp-radius-md);
    border-left: 3px solid var(--vp-info);
}

.comment-notes .required {
    color: var(--vp-error);
    font-weight: var(--vp-font-weight-semibold);
}

/* Form Submit */
.form-submit {
    margin-bottom: 0;
    text-align: right;
}

.form-submit .vp-button {
    background: linear-gradient(135deg, var(--vp-primary) 0%, var(--vp-primary-dark) 100%);
    border: 2px solid var(--vp-primary);
    padding: var(--vp-space-3) var(--vp-space-8);
    font-size: var(--vp-font-size-base);
    font-weight: var(--vp-font-weight-semibold);
}

.form-submit .vp-button:hover {
    background: linear-gradient(135deg, var(--vp-primary-dark) 0%, var(--vp-primary) 100%);
    transform: translateY(-2px);
    box-shadow: var(--vp-shadow-lg);
}

/* No Comments Message */
.no-comments {
    text-align: center;
    padding: var(--vp-space-8);
    color: var(--vp-text-secondary);
    font-style: italic;
    background-color: var(--vp-background-alt);
    border-radius: var(--vp-radius-lg);
    border: 1px solid var(--vp-border-light);
}

/* Responsive Design */
@media (max-width: 768px) {
    .comments-area {
        margin-top: var(--vp-space-8);
        padding-top: var(--vp-space-6);
    }
    
    .comment-list .comment {
        padding: var(--vp-space-4);
        margin-bottom: var(--vp-space-4);
    }
    
    .comment-author {
        flex-direction: column;
        text-align: center;
        gap: var(--vp-space-2);
    }
    
    .comment-list .children {
        margin-left: var(--vp-space-4);
    }
    
    .comment-respond {
        padding: var(--vp-space-6);
        margin-top: var(--vp-space-6);
    }
    
    .comment-navigation {
        flex-direction: column;
        gap: var(--vp-space-2);
        text-align: center;
    }
    
    .comment-navigation .nav-next {
        text-align: center;
    }
}

@media (max-width: 480px) {
    .comments-title {
        font-size: var(--vp-font-size-xl);
    }
    
    .comment-reply-title {
        font-size: var(--vp-font-size-xl);
    }
    
    .form-submit {
        text-align: center;
    }
}
