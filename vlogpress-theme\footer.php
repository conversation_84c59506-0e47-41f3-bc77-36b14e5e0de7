<?php
/**
 * The template for displaying the footer
 *
 * Contains the closing of the #content div and all content after.
 *
 * @package VlogPress
 * @since 1.0.0
 */

?>

    </div><!-- #content -->

    <footer id="colophon" class="site-footer" role="contentinfo">
        <?php if ( is_active_sidebar( 'footer-1' ) || is_active_sidebar( 'footer-2' ) || is_active_sidebar( 'footer-3' ) ) : ?>
            <div class="footer-widgets">
                <div class="vp-container">
                    <?php if ( is_active_sidebar( 'footer-1' ) ) : ?>
                        <div class="footer-widget-area">
                            <?php dynamic_sidebar( 'footer-1' ); ?>
                        </div>
                    <?php endif; ?>

                    <?php if ( is_active_sidebar( 'footer-2' ) ) : ?>
                        <div class="footer-widget-area">
                            <?php dynamic_sidebar( 'footer-2' ); ?>
                        </div>
                    <?php endif; ?>

                    <?php if ( is_active_sidebar( 'footer-3' ) ) : ?>
                        <div class="footer-widget-area">
                            <?php dynamic_sidebar( 'footer-3' ); ?>
                        </div>
                    <?php endif; ?>
                </div>
            </div><!-- .footer-widgets -->
        <?php endif; ?>

        <div class="footer-info">
            <div class="vp-container">
                <div class="footer-branding">
                    <?php if ( has_custom_logo() ) : ?>
                        <div class="footer-logo">
                            <?php
                            $custom_logo_id = get_theme_mod( 'custom_logo' );
                            $logo = wp_get_attachment_image_src( $custom_logo_id, 'full' );
                            if ( $logo ) {
                                echo '<img src="' . esc_url( $logo[0] ) . '" alt="' . esc_attr( get_bloginfo( 'name' ) ) . '" class="footer-logo">';
                            }
                            ?>
                        </div>
                    <?php else : ?>
                        <div class="footer-text">
                            <strong><?php bloginfo( 'name' ); ?></strong>
                        </div>
                    <?php endif; ?>

                    <p class="footer-text">
                        <?php
                        printf(
                            esc_html__( '© %1$s %2$s. All rights reserved.', 'vlogpress' ),
                            date( 'Y' ),
                            get_bloginfo( 'name' )
                        );
                        ?>
                    </p>
                </div>

                <div class="footer-navigation-wrapper">
                    <?php if ( has_nav_menu( 'footer' ) ) : ?>
                        <nav class="footer-navigation" role="navigation" aria-label="<?php esc_attr_e( 'Footer Menu', 'vlogpress' ); ?>">
                            <?php
                            wp_nav_menu( array(
                                'theme_location' => 'footer',
                                'menu_class'     => 'footer-menu',
                                'container'      => false,
                                'depth'          => 1,
                            ) );
                            ?>
                        </nav><!-- .footer-navigation -->
                    <?php endif; ?>

                    <?php if ( has_nav_menu( 'social' ) ) : ?>
                        <div class="footer-social">
                            <?php
                            wp_nav_menu( array(
                                'theme_location' => 'social',
                                'menu_class'     => 'social-links-menu',
                                'container'      => false,
                                'depth'          => 1,
                                'link_before'    => '<span class="vp-sr-only">',
                                'link_after'     => '</span>',
                            ) );
                            ?>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div><!-- .footer-info -->

        <div class="footer-copyright">
            <div class="vp-container">
                <p>
                    <?php
                    printf(
                        esc_html__( 'Powered by %1$s | Professional theme by %2$s', 'vlogpress' ),
                        '<a href="' . esc_url( __( 'https://wordpress.org/', 'vlogpress' ) ) . '">WordPress</a>',
                        '<a href="#" rel="designer">VlogPress</a>'
                    );
                    ?>
                </p>
            </div>
        </div><!-- .footer-copyright -->
    </footer><!-- #colophon -->

</div><!-- #page -->

<?php wp_footer(); ?>

</body>
</html>