/* ==========================================================================
   Main Layout Components - Professional Fortune 500 Style
   ========================================================================== */

/* Site Structure */
.site {
    min-height: 100vh;
    display: flex;
    flex-direction: column;
}

.site-content {
    flex: 1;
    padding-top: var(--vp-space-8);
    padding-bottom: var(--vp-space-12);
}

/* Container System */
.vp-container {
    width: 100%;
    max-width: var(--vp-container-xl);
    margin: 0 auto;
    padding-left: var(--vp-space-4);
    padding-right: var(--vp-space-4);
}

.vp-container-sm {
    max-width: var(--vp-container-sm);
}

.vp-container-md {
    max-width: var(--vp-container-md);
}

.vp-container-lg {
    max-width: var(--vp-container-lg);
}

.vp-container-2xl {
    max-width: var(--vp-container-2xl);
}

/* Grid System */
.vp-grid {
    display: grid;
    gap: var(--vp-space-6);
}

.vp-grid-cols-1 { grid-template-columns: repeat(1, minmax(0, 1fr)); }
.vp-grid-cols-2 { grid-template-columns: repeat(2, minmax(0, 1fr)); }
.vp-grid-cols-3 { grid-template-columns: repeat(3, minmax(0, 1fr)); }
.vp-grid-cols-4 { grid-template-columns: repeat(4, minmax(0, 1fr)); }

/* Flex System */
.vp-flex {
    display: flex;
    gap: var(--vp-space-4);
}

.vp-flex-col {
    flex-direction: column;
}

.vp-flex-wrap {
    flex-wrap: wrap;
}

.vp-items-center {
    align-items: center;
}

.vp-items-start {
    align-items: flex-start;
}

.vp-items-end {
    align-items: flex-end;
}

.vp-justify-center {
    justify-content: center;
}

.vp-justify-between {
    justify-content: space-between;
}

.vp-justify-start {
    justify-content: flex-start;
}

.vp-justify-end {
    justify-content: flex-end;
}

/* Content Grid - Professional card layout */
.vp-content-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
    gap: var(--vp-space-8);
    margin-bottom: var(--vp-space-12);
}

/* Content Cards */
.vp-content-card {
    background-color: var(--vp-background);
    border-radius: var(--vp-radius-xl);
    box-shadow: var(--vp-shadow-sm);
    overflow: hidden;
    transition: all var(--vp-transition-normal);
    border: 1px solid var(--vp-border-light);
    position: relative;
}

.vp-content-card:hover {
    box-shadow: var(--vp-shadow-lg);
    transform: translateY(-2px);
    border-color: var(--vp-border);
}

.vp-card-thumbnail {
    position: relative;
    overflow: hidden;
    aspect-ratio: 16 / 9;
    background-color: var(--vp-background-subtle);
}

.vp-card-thumbnail img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform var(--vp-transition-slow);
}

.vp-content-card:hover .vp-card-thumbnail img {
    transform: scale(1.05);
}

.vp-card-content {
    padding: var(--vp-space-6);
}

.vp-card-header {
    margin-bottom: var(--vp-space-4);
}

.vp-card-title {
    margin: 0 0 var(--vp-space-3) 0;
    font-size: var(--vp-font-size-xl);
    font-weight: var(--vp-font-weight-semibold);
    line-height: var(--vp-line-height-tight);
}

.vp-card-title a {
    color: var(--vp-text-primary);
    text-decoration: none;
    transition: color var(--vp-transition-fast);
}

.vp-card-title a:hover {
    color: var(--vp-primary);
}

.vp-card-meta {
    display: flex;
    align-items: center;
    gap: var(--vp-space-4);
    flex-wrap: wrap;
    font-size: var(--vp-font-size-sm);
    color: var(--vp-text-tertiary);
}

.vp-content-type {
    display: inline-flex;
    align-items: center;
    padding: var(--vp-space-1) var(--vp-space-2);
    border-radius: var(--vp-radius-full);
    font-size: var(--vp-font-size-xs);
    font-weight: var(--vp-font-weight-medium);
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

.vp-type-blog {
    background-color: var(--vp-info-light);
    color: var(--vp-info);
}

.vp-type-video {
    background-color: var(--vp-error-light);
    color: var(--vp-error);
}

.vp-published-date,
.vp-reading-time,
.vp-view-count {
    display: flex;
    align-items: center;
    gap: var(--vp-space-1);
}

.vp-card-excerpt {
    margin-bottom: var(--vp-space-6);
    color: var(--vp-text-secondary);
    line-height: var(--vp-line-height-relaxed);
}

.vp-card-footer {
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: var(--vp-space-4);
}

.vp-read-more {
    display: inline-flex;
    align-items: center;
    gap: var(--vp-space-2);
    padding: var(--vp-space-3) var(--vp-space-5);
    background-color: var(--vp-primary);
    color: var(--vp-text-inverse);
    text-decoration: none;
    border-radius: var(--vp-radius-lg);
    font-weight: var(--vp-font-weight-medium);
    font-size: var(--vp-font-size-sm);
    transition: all var(--vp-transition-fast);
    border: 2px solid var(--vp-primary);
}

.vp-read-more:hover {
    background-color: var(--vp-primary-hover);
    border-color: var(--vp-primary-hover);
    transform: translateY(-1px);
    box-shadow: var(--vp-shadow-md);
}

.vp-read-more:focus {
    outline: none;
    box-shadow: 0 0 0 3px var(--vp-primary-light);
}

.vp-card-terms {
    display: flex;
    align-items: center;
    gap: var(--vp-space-2);
}

.vp-category {
    padding: var(--vp-space-1) var(--vp-space-2);
    background-color: var(--vp-background-subtle);
    color: var(--vp-text-tertiary);
    border-radius: var(--vp-radius-md);
    font-size: var(--vp-font-size-xs);
    font-weight: var(--vp-font-weight-medium);
}

/* Page Header */
.page-header {
    text-align: center;
    margin-bottom: var(--vp-space-12);
    padding: var(--vp-space-8) 0;
    background: linear-gradient(135deg, var(--vp-background-alt) 0%, var(--vp-background) 100%);
    border-radius: var(--vp-radius-2xl);
}

.page-title {
    margin: 0;
    font-size: var(--vp-font-size-4xl);
    font-weight: var(--vp-font-weight-bold);
    color: var(--vp-text-primary);
    line-height: var(--vp-line-height-tight);
}

/* No Results Section */
.vp-no-results {
    text-align: center;
    padding: var(--vp-space-16) var(--vp-space-4);
}

.vp-no-results .page-header {
    background: none;
    padding: 0;
    margin-bottom: var(--vp-space-8);
}

.vp-no-results .page-content {
    max-width: 600px;
    margin: 0 auto;
}

.vp-no-results p {
    font-size: var(--vp-font-size-lg);
    color: var(--vp-text-secondary);
    margin-bottom: var(--vp-space-6);
}

/* Pagination */
.vp-pagination {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: var(--vp-space-2);
    margin-top: var(--vp-space-12);
}

.vp-pagination .page-numbers {
    display: flex;
    align-items: center;
    justify-content: center;
    min-width: 44px;
    height: 44px;
    padding: var(--vp-space-2) var(--vp-space-3);
    border: 1px solid var(--vp-border);
    border-radius: var(--vp-radius-lg);
    color: var(--vp-text-secondary);
    text-decoration: none;
    font-weight: var(--vp-font-weight-medium);
    transition: all var(--vp-transition-fast);
}

.vp-pagination .page-numbers:hover,
.vp-pagination .page-numbers.current {
    background-color: var(--vp-primary);
    border-color: var(--vp-primary);
    color: var(--vp-text-inverse);
}

.vp-pagination .page-numbers.dots {
    border: none;
    background: none;
    color: var(--vp-text-tertiary);
}

/* Responsive Design */
@media (min-width: 640px) {
    .vp-container {
        padding-left: var(--vp-space-6);
        padding-right: var(--vp-space-6);
    }
}

@media (min-width: 1024px) {
    .vp-container {
        padding-left: var(--vp-space-8);
        padding-right: var(--vp-space-8);
    }
    
    .vp-content-grid {
        grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));
        gap: var(--vp-space-10);
    }
}

@media (max-width: 768px) {
    .site-content {
        padding-top: var(--vp-space-6);
        padding-bottom: var(--vp-space-8);
    }
    
    .vp-content-grid {
        grid-template-columns: 1fr;
        gap: var(--vp-space-6);
        margin-bottom: var(--vp-space-8);
    }
    
    .vp-card-content {
        padding: var(--vp-space-5);
    }
    
    .vp-card-title {
        font-size: var(--vp-font-size-lg);
    }
    
    .vp-card-footer {
        flex-direction: column;
        align-items: stretch;
        gap: var(--vp-space-3);
    }
    
    .page-header {
        padding: var(--vp-space-6) var(--vp-space-4);
        margin-bottom: var(--vp-space-8);
    }
    
    .page-title {
        font-size: var(--vp-font-size-3xl);
    }
}

@media (max-width: 480px) {
    .vp-card-meta {
        flex-direction: column;
        align-items: flex-start;
        gap: var(--vp-space-2);
    }
    
    .vp-read-more {
        width: 100%;
        justify-content: center;
    }
}
