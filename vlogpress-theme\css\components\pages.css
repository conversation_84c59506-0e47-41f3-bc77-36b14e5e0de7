/* ==========================================================================
   Page-Specific Styles - Vlogs and Blogs Pages
   ========================================================================== */

/* Common Archive Page Styles */
.content-archive .page-header {
    background: linear-gradient(135deg, var(--vp-primary-light) 0%, var(--vp-background-alt) 100%);
    border: 1px solid var(--vp-border-light);
    margin-bottom: var(--vp-space-12);
}

.content-archive .page-icon {
    color: var(--vp-primary);
    margin-right: var(--vp-space-2);
    vertical-align: text-bottom;
}

.content-archive .page-description {
    font-size: var(--vp-font-size-lg);
    color: var(--vp-text-secondary);
    margin: var(--vp-space-4) 0 0;
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
}

/* Filter Buttons */
.vlogs-filters,
.blogs-filters {
    margin-bottom: var(--vp-space-8);
    text-align: center;
}

.filter-buttons {
    display: inline-flex;
    flex-wrap: wrap;
    gap: var(--vp-space-2);
    background-color: var(--vp-background-alt);
    padding: var(--vp-space-2);
    border-radius: var(--vp-radius-xl);
    border: 1px solid var(--vp-border-light);
}

.filter-btn {
    padding: var(--vp-space-2) var(--vp-space-4);
    background-color: transparent;
    color: var(--vp-text-secondary);
    border: none;
    border-radius: var(--vp-radius-lg);
    font-size: var(--vp-font-size-sm);
    font-weight: var(--vp-font-weight-medium);
    cursor: pointer;
    transition: all var(--vp-transition-fast);
    white-space: nowrap;
}

.filter-btn:hover {
    background-color: var(--vp-background);
    color: var(--vp-text-primary);
}

.filter-btn.active {
    background-color: var(--vp-primary);
    color: var(--vp-text-inverse);
}

/* Vlogs Page Specific Styles */
.vlogs-page .page-header {
    background: linear-gradient(135deg, var(--vp-error-light) 0%, var(--vp-background-alt) 100%);
}

.vlogs-page .page-icon {
    color: var(--vp-error);
}

.vlogs-page .vp-content-grid {
    grid-template-columns: repeat(auto-fill, minmax(380px, 1fr));
}

.vlog-card {
    border-left: 4px solid var(--vp-error);
    position: relative;
}

.vlog-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, transparent 0%, rgba(239, 68, 68, 0.02) 100%);
    pointer-events: none;
    border-radius: var(--vp-radius-xl);
}

.vlog-card:hover {
    border-left-color: var(--vp-error);
    box-shadow: var(--vp-shadow-lg), -4px 0 0 var(--vp-error);
}

.vlog-thumbnail {
    position: relative;
    background: linear-gradient(135deg, var(--vp-error-light) 0%, var(--vp-background-alt) 100%);
}

.vlog-thumbnail::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, transparent 0%, rgba(239, 68, 68, 0.1) 100%);
    pointer-events: none;
}

.vp-watch-video {
    background: linear-gradient(135deg, var(--vp-error) 0%, #dc2626 100%);
    border-color: var(--vp-error);
}

.vp-watch-video:hover {
    background: linear-gradient(135deg, #dc2626 0%, #b91c1c 100%);
    border-color: #dc2626;
}

.vp-video-duration-meta svg,
.vp-view-count svg {
    color: var(--vp-error);
}

/* Blogs Page Specific Styles */
.blogs-page .page-header {
    background: linear-gradient(135deg, var(--vp-info-light) 0%, var(--vp-background-alt) 100%);
}

.blogs-page .page-icon {
    color: var(--vp-info);
}

.blogs-page .vp-content-grid {
    grid-template-columns: repeat(auto-fill, minmax(360px, 1fr));
}

.blog-card {
    border-left: 4px solid var(--vp-info);
    position: relative;
}

.blog-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, transparent 0%, rgba(49, 130, 206, 0.02) 100%);
    pointer-events: none;
    border-radius: var(--vp-radius-xl);
}

.blog-card:hover {
    border-left-color: var(--vp-info);
    box-shadow: var(--vp-shadow-lg), -4px 0 0 var(--vp-info);
}

.blog-thumbnail {
    background: linear-gradient(135deg, var(--vp-info-light) 0%, var(--vp-background-alt) 100%);
}

.vp-read-article {
    background: linear-gradient(135deg, var(--vp-info) 0%, #2b77cb 100%);
    border-color: var(--vp-info);
}

.vp-read-article:hover {
    background: linear-gradient(135deg, #2b77cb 0%, #2563eb 100%);
    border-color: #2b77cb;
}

.vp-reading-time svg,
.vp-author svg {
    color: var(--vp-info);
}

/* Enhanced Card Meta Styling */
.vlogs-page .vp-card-meta,
.blogs-page .vp-card-meta {
    display: flex;
    align-items: center;
    gap: var(--vp-space-3);
    flex-wrap: wrap;
    margin-bottom: var(--vp-space-4);
}

.vlogs-page .vp-card-meta > *,
.blogs-page .vp-card-meta > * {
    display: flex;
    align-items: center;
    gap: var(--vp-space-1);
    font-size: var(--vp-font-size-xs);
    color: var(--vp-text-tertiary);
    font-weight: var(--vp-font-weight-medium);
}

.vlogs-page .vp-content-type,
.blogs-page .vp-content-type {
    font-size: var(--vp-font-size-xs);
    padding: var(--vp-space-1) var(--vp-space-2);
    border-radius: var(--vp-radius-full);
    font-weight: var(--vp-font-weight-semibold);
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

/* No Results Styling */
.vlogs-no-results,
.blogs-no-results {
    text-align: center;
    padding: var(--vp-space-16) var(--vp-space-4);
    background: linear-gradient(135deg, var(--vp-background-alt) 0%, var(--vp-background) 100%);
    border-radius: var(--vp-radius-2xl);
    border: 1px solid var(--vp-border-light);
}

.vlogs-no-results .page-header,
.blogs-no-results .page-header {
    background: none;
    padding: 0;
    margin-bottom: var(--vp-space-6);
    border: none;
}

/* Pagination Styling */
.vlogs-pagination,
.blogs-pagination {
    margin-top: var(--vp-space-12);
}

.vlogs-pagination .page-numbers,
.blogs-pagination .page-numbers {
    border-radius: var(--vp-radius-lg);
    font-weight: var(--vp-font-weight-medium);
}

.vlogs-pagination .page-numbers.current,
.vlogs-pagination .page-numbers:hover {
    background-color: var(--vp-error);
    border-color: var(--vp-error);
}

.blogs-pagination .page-numbers.current,
.blogs-pagination .page-numbers:hover {
    background-color: var(--vp-info);
    border-color: var(--vp-info);
}

/* Responsive Design */
@media (max-width: 768px) {
    .vlogs-page .vp-content-grid,
    .blogs-page .vp-content-grid {
        grid-template-columns: 1fr;
        gap: var(--vp-space-6);
    }
    
    .filter-buttons {
        flex-direction: column;
        align-items: stretch;
        gap: var(--vp-space-1);
    }
    
    .filter-btn {
        text-align: center;
        padding: var(--vp-space-3) var(--vp-space-4);
    }
    
    .content-archive .page-description {
        font-size: var(--vp-font-size-base);
    }
    
    .vlogs-page .vp-card-meta,
    .blogs-page .vp-card-meta {
        flex-direction: column;
        align-items: flex-start;
        gap: var(--vp-space-2);
    }
}

@media (max-width: 480px) {
    .content-archive .page-header {
        padding: var(--vp-space-6) var(--vp-space-4);
        margin-bottom: var(--vp-space-8);
    }
    
    .vlogs-filters,
    .blogs-filters {
        margin-bottom: var(--vp-space-6);
    }
    
    .vlogs-no-results,
    .blogs-no-results {
        padding: var(--vp-space-12) var(--vp-space-4);
    }
}
