# Separate Vlog and Blog Pages Guide

## Overview
The VlogPress theme now features dedicated pages for video content (vlogs) and blog articles, providing users with a clear separation between different content types and improved navigation experience.

## Features Implemented

### 1. Dedicated Page Templates
- **`page-vlogs.php`**: Template for displaying all video content
- **`page-blogs.php`**: Template for displaying all blog articles
- Both templates include filtering, pagination, and responsive design

### 2. Automatic Page Creation
When the theme is activated, it automatically creates:
- **Videos Page** (`/vlogs/`): Displays all vlog posts
- **Blog Page** (`/blogs/`): Displays all regular blog posts

### 3. Enhanced Navigation
- Updated header navigation with separate "Videos" and "Blog" links
- Icons for visual distinction (play icon for videos, document icon for blog)
- Fallback menu system for sites without custom menus

### 4. Content Filtering
- Category-based filtering on both pages
- Smooth animations when switching between filters
- URL parameter support for bookmarkable filtered views

### 5. Visual Distinction
- **Videos Page**: Red accent color scheme with video-specific styling
- **Blog Page**: Blue accent color scheme with article-specific styling
- Different card layouts and hover effects for each content type

## Technical Implementation

### Page Templates
```php
// Videos page query
$vlogs_query = new WP_Query( array(
    'post_type'      => 'vlog',
    'post_status'    => 'publish',
    'posts_per_page' => get_option( 'posts_per_page' ),
    'paged'          => $paged,
) );

// Blog page query
$blogs_query = new WP_Query( array(
    'post_type'      => 'post',
    'post_status'    => 'publish',
    'posts_per_page' => get_option( 'posts_per_page' ),
    'paged'          => $paged,
) );
```

### Custom Functions Added
- `vlogpress_get_vlogs()`: Get vlogs with optional filtering
- `vlogpress_get_blogs()`: Get blog posts with optional filtering
- `vlogpress_is_vlogs_page()`: Check if current page is vlogs page
- `vlogpress_is_blogs_page()`: Check if current page is blogs page
- `vlogpress_get_content_count()`: Get content count by type

### CSS Architecture
```
css/components/pages.css
├── Common archive page styles
├── Filter button styling
├── Vlogs page specific styles
├── Blogs page specific styles
└── Responsive design
```

### JavaScript Features
- Content filtering with smooth animations
- URL parameter management for filters
- Progressive enhancement for better UX
- Intersection Observer for card animations

## Usage Instructions

### For Site Administrators

#### 1. Accessing the Pages
After theme activation, you'll find:
- **Videos page**: `yoursite.com/vlogs/`
- **Blog page**: `yoursite.com/blogs/`

#### 2. Customizing Page Content
1. Go to **Pages** in WordPress admin
2. Edit the "Videos" or "Blog" page
3. Modify the page content, title, or featured image
4. The page template will automatically handle the content display

#### 3. Menu Integration
1. Go to **Appearance > Menus**
2. Add the Videos and Blog pages to your menu
3. The theme will automatically add icons to these menu items

#### 4. Content Management
- **Create Videos**: Use **Vlogs > Add New** in admin
- **Create Blog Posts**: Use **Posts > Add New** in admin
- Both content types will automatically appear on their respective pages

### For Developers

#### 1. Customizing Templates
```php
// Override templates by copying to child theme
page-vlogs.php    // Videos page template
page-blogs.php    // Blog page template
```

#### 2. Adding Custom Filters
```php
// Add custom query modifications
function custom_vlogs_query( $query ) {
    if ( vlogpress_is_vlogs_page() && $query->is_main_query() ) {
        // Custom query modifications
    }
}
add_action( 'pre_get_posts', 'custom_vlogs_query' );
```

#### 3. Styling Customization
```css
/* Target specific page styles */
.vlogs-page .custom-element { }
.blogs-page .custom-element { }

/* Override filter buttons */
.filter-btn.custom-filter { }
```

## Content Display Features

### Videos Page
- **Video thumbnails** with play overlay
- **Duration display** for each video
- **View count** (if available)
- **"Watch Video" buttons** with video icon
- **Red accent theme** for video branding

### Blog Page
- **Article thumbnails** or featured images
- **Reading time estimation**
- **Author information**
- **"Read Article" buttons** with document icon
- **Blue accent theme** for blog branding

### Common Features
- **Category filtering** with smooth animations
- **Responsive grid layout** that adapts to screen size
- **Professional pagination** with proper navigation
- **SEO-friendly URLs** and meta information
- **Accessibility compliance** with proper ARIA labels

## Responsive Design

### Mobile Optimization
- **Single column layout** on mobile devices
- **Touch-friendly filter buttons**
- **Optimized card spacing** for mobile viewing
- **Collapsible filter interface**

### Tablet & Desktop
- **Multi-column grid** that adapts to screen width
- **Hover effects** for better interactivity
- **Optimal reading experience** across devices

## SEO & Performance

### SEO Benefits
- **Separate URLs** for different content types
- **Proper meta titles** and descriptions
- **Structured content organization**
- **Category-based filtering** for better content discovery

### Performance Features
- **Lazy loading** for images and videos
- **Efficient queries** with proper pagination
- **Minimal JavaScript** for filtering functionality
- **Optimized CSS** with component-based architecture

## Customization Options

### Theme Customizer Integration
The pages respect all theme customizer settings:
- Color schemes
- Typography choices
- Layout preferences
- Custom logos and branding

### Widget Areas
Both pages support:
- Sidebar widgets (if enabled)
- Footer widgets
- Custom widget areas (with code modifications)

## Troubleshooting

### Common Issues

#### 1. Pages Not Found (404)
- Go to **Settings > Permalinks** and click "Save Changes"
- This will flush the rewrite rules

#### 2. Content Not Displaying
- Ensure you have published vlogs and blog posts
- Check that the post types are properly registered

#### 3. Filters Not Working
- Ensure JavaScript is enabled
- Check browser console for any errors
- Verify categories are assigned to posts

#### 4. Styling Issues
- Clear any caching plugins
- Ensure all CSS files are properly enqueued
- Check for theme conflicts

### Support
For additional support or customization requests, refer to the theme documentation or contact the theme developer.

## Future Enhancements
Potential future improvements could include:
- Advanced search functionality
- Content type mixing options
- Custom post type support
- Enhanced filtering options
- Social sharing integration
- Content recommendation system
