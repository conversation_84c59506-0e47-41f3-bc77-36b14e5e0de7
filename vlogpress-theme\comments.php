<?php
/**
 * The template for displaying comments
 *
 * This is the template that displays the area of the page that contains both the current comments
 * and the comment form.
 *
 * @package VlogPress
 * @since 1.0.0
 */

/*
 * If the current post is protected by a password and
 * the visitor has not yet entered the password we will
 * return early without loading the comments.
 */
if ( post_password_required() ) {
    return;
}
?>

<div id="comments" class="comments-area">

    <?php
    // You can start editing here -- including this comment!
    if ( have_comments() ) :
        ?>
        <h2 class="comments-title">
            <?php
            $vlogpress_comment_count = get_comments_number();
            if ( '1' === $vlogpress_comment_count ) {
                printf(
                    /* translators: 1: title. */
                    esc_html__( 'One thought on &ldquo;%1$s&rdquo;', 'vlogpress' ),
                    '<span>' . wp_kses_post( get_the_title() ) . '</span>'
                );
            } else {
                printf(
                    /* translators: 1: comment count number, 2: title. */
                    esc_html( _nx( '%1$s thought on &ldquo;%2$s&rdquo;', '%1$s thoughts on &ldquo;%2$s&rdquo;', $vlogpress_comment_count, 'comments title', 'vlogpress' ) ),
                    number_format_i18n( $vlogpress_comment_count ), // phpcs:ignore WordPress.Security.EscapeOutput.OutputNotEscaped
                    '<span>' . wp_kses_post( get_the_title() ) . '</span>'
                );
            }
            ?>
        </h2><!-- .comments-title -->

        <?php the_comments_navigation(); ?>

        <ol class="comment-list">
            <?php
            wp_list_comments(
                array(
                    'style'      => 'ol',
                    'short_ping' => true,
                    'callback'   => 'vlogpress_comment_callback',
                )
            );
            ?>
        </ol><!-- .comment-list -->

        <?php
        the_comments_navigation();

        // If comments are closed and there are comments, let's leave a little note, shall we?
        if ( ! comments_open() ) :
            ?>
            <p class="no-comments"><?php esc_html_e( 'Comments are closed.', 'vlogpress' ); ?></p>
            <?php
        endif;

    endif; // Check for have_comments().

    // Comment form
    $comment_form_args = array(
        'title_reply_before' => '<h3 id="reply-title" class="comment-reply-title">',
        'title_reply_after'  => '</h3>',
        'title_reply'        => __( 'Leave a Reply', 'vlogpress' ),
        'title_reply_to'     => __( 'Leave a Reply to %s', 'vlogpress' ),
        'cancel_reply_before' => '<small>',
        'cancel_reply_after'  => '</small>',
        'cancel_reply_link'   => __( 'Cancel reply', 'vlogpress' ),
        'label_submit'       => __( 'Post Comment', 'vlogpress' ),
        'submit_button'      => '<input name="%1$s" type="submit" id="%2$s" class="%3$s vp-button" value="%4$s" />',
        'submit_field'       => '<p class="form-submit">%1$s %2$s</p>',
        'format'             => 'xhtml',
        'comment_field'      => '<p class="comment-form-comment"><label for="comment">' . _x( 'Comment', 'noun', 'vlogpress' ) . ' <span class="required">*</span></label> <textarea id="comment" name="comment" cols="45" rows="8" maxlength="65525" required="required" placeholder="' . esc_attr__( 'Write your comment here...', 'vlogpress' ) . '"></textarea></p>',
        'must_log_in'        => '<p class="must-log-in">' . sprintf(
            /* translators: %s: login URL */
            __( 'You must be <a href="%s">logged in</a> to post a comment.', 'vlogpress' ),
            wp_login_url( apply_filters( 'the_permalink', get_permalink( get_the_ID() ), get_the_ID() ) )
        ) . '</p>',
        'logged_in_as'       => '<p class="logged-in-as">' . sprintf(
            /* translators: 1: edit user link, 2: accessibility text, 3: user name, 4: logout URL */
            __( '<a href="%1$s" aria-label="%2$s">Logged in as %3$s</a>. <a href="%4$s">Log out?</a>', 'vlogpress' ),
            get_edit_user_link(),
            /* translators: %s: user name */
            esc_attr( sprintf( __( 'Logged in as %s. Edit your profile.', 'vlogpress' ), $user_identity ) ),
            $user_identity,
            wp_logout_url( apply_filters( 'the_permalink', get_permalink( get_the_ID() ), get_the_ID() ) )
        ) . '</p>',
        'comment_notes_before' => '<p class="comment-notes"><span id="email-notes">' . __( 'Your email address will not be published.', 'vlogpress' ) . '</span> ' . ( $req ? __( 'Required fields are marked <span class="required">*</span>', 'vlogpress' ) : '' ) . '</p>',
        'comment_notes_after'  => '',
        'id_form'            => 'commentform',
        'id_submit'          => 'submit',
        'class_form'         => 'comment-form',
        'class_submit'       => 'submit',
        'name_submit'        => 'submit',
        'fields'             => array(
            'author' => '<p class="comment-form-author">' .
                        '<label for="author">' . __( 'Name', 'vlogpress' ) . ( $req ? ' <span class="required">*</span>' : '' ) . '</label> ' .
                        '<input id="author" name="author" type="text" value="' . esc_attr( $commenter['comment_author'] ) . '" size="30" maxlength="245"' . $aria_req . ' placeholder="' . esc_attr__( 'Your name', 'vlogpress' ) . '" /></p>',
            'email'  => '<p class="comment-form-email">' .
                        '<label for="email">' . __( 'Email', 'vlogpress' ) . ( $req ? ' <span class="required">*</span>' : '' ) . '</label> ' .
                        '<input id="email" name="email" type="email" value="' . esc_attr( $commenter['comment_author_email'] ) . '" size="30" maxlength="100" aria-describedby="email-notes"' . $aria_req . ' placeholder="' . esc_attr__( '<EMAIL>', 'vlogpress' ) . '" /></p>',
            'url'    => '<p class="comment-form-url">' .
                        '<label for="url">' . __( 'Website', 'vlogpress' ) . '</label> ' .
                        '<input id="url" name="url" type="url" value="' . esc_attr( $commenter['comment_author_url'] ) . '" size="30" maxlength="200" placeholder="' . esc_attr__( 'https://yourwebsite.com', 'vlogpress' ) . '" /></p>',
        ),
    );

    comment_form( $comment_form_args );
    ?>

</div><!-- #comments -->

<?php
/**
 * Custom comment callback function
 */
function vlogpress_comment_callback( $comment, $args, $depth ) {
    if ( 'div' === $args['style'] ) {
        $tag       = 'div';
        $add_below = 'comment';
    } else {
        $tag       = 'li';
        $add_below = 'div-comment';
    }
    ?>
    <<?php echo $tag; ?> <?php comment_class( empty( $args['has_children'] ) ? '' : 'parent' ); ?> id="comment-<?php comment_ID(); ?>">
    <?php if ( 'div' !== $args['style'] ) : ?>
        <div id="div-comment-<?php comment_ID(); ?>" class="comment-body">
    <?php endif; ?>
    
    <div class="comment-author vcard">
        <?php if ( $args['avatar_size'] !== 0 ) : ?>
            <div class="comment-avatar">
                <?php echo get_avatar( $comment, $args['avatar_size'] ); ?>
            </div>
        <?php endif; ?>
        
        <div class="comment-metadata">
            <?php printf( '<cite class="fn">%s</cite>', get_comment_author_link() ); ?>
            <div class="comment-meta commentmetadata">
                <a href="<?php echo htmlspecialchars( get_comment_link( $comment->comment_ID ) ); ?>">
                    <?php
                    /* translators: 1: date, 2: time */
                    printf( __( '%1$s at %2$s', 'vlogpress' ), get_comment_date(), get_comment_time() );
                    ?>
                </a>
                <?php edit_comment_link( __( '(Edit)', 'vlogpress' ), '  ', '' ); ?>
            </div>
        </div>
    </div>

    <?php if ( $comment->comment_approved === '0' ) : ?>
        <em class="comment-awaiting-moderation"><?php _e( 'Your comment is awaiting moderation.', 'vlogpress' ); ?></em>
        <br />
    <?php endif; ?>

    <div class="comment-content">
        <?php comment_text(); ?>
    </div>

    <div class="reply">
        <?php
        comment_reply_link(
            array_merge(
                $args,
                array(
                    'add_below' => $add_below,
                    'depth'     => $depth,
                    'max_depth' => $args['max_depth'],
                )
            )
        );
        ?>
    </div>
    
    <?php if ( 'div' !== $args['style'] ) : ?>
        </div>
    <?php endif; ?>
    <?php
}
