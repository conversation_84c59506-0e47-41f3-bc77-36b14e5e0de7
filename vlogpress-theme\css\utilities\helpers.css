/* ==========================================================================
   Utility Classes - Professional Fortune 500 Style
   ========================================================================== */

/* Accessibility */
.vp-sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
}

.vp-skip-link {
    position: absolute;
    top: -40px;
    left: 6px;
    background: var(--vp-primary);
    color: var(--vp-text-inverse);
    padding: var(--vp-space-2) var(--vp-space-4);
    text-decoration: none;
    border-radius: var(--vp-radius-md);
    font-weight: var(--vp-font-weight-medium);
    z-index: 100000;
    transition: top var(--vp-transition-fast);
}

.vp-skip-link:focus {
    top: 6px;
}

/* Display Utilities */
.vp-hidden { display: none !important; }
.vp-block { display: block !important; }
.vp-inline-block { display: inline-block !important; }
.vp-inline { display: inline !important; }
.vp-flex { display: flex !important; }
.vp-inline-flex { display: inline-flex !important; }
.vp-grid { display: grid !important; }

/* Visibility */
.vp-visible { visibility: visible !important; }
.vp-invisible { visibility: hidden !important; }

/* Text Alignment */
.vp-text-left { text-align: left !important; }
.vp-text-center { text-align: center !important; }
.vp-text-right { text-align: right !important; }
.vp-text-justify { text-align: justify !important; }

/* Text Colors */
.vp-text-primary { color: var(--vp-text-primary) !important; }
.vp-text-secondary { color: var(--vp-text-secondary) !important; }
.vp-text-tertiary { color: var(--vp-text-tertiary) !important; }
.vp-text-light { color: var(--vp-text-light) !important; }
.vp-text-inverse { color: var(--vp-text-inverse) !important; }
.vp-text-success { color: var(--vp-success) !important; }
.vp-text-warning { color: var(--vp-warning) !important; }
.vp-text-error { color: var(--vp-error) !important; }
.vp-text-info { color: var(--vp-info) !important; }

/* Background Colors */
.vp-bg-primary { background-color: var(--vp-primary) !important; }
.vp-bg-secondary { background-color: var(--vp-secondary) !important; }
.vp-bg-white { background-color: var(--vp-background) !important; }
.vp-bg-alt { background-color: var(--vp-background-alt) !important; }
.vp-bg-subtle { background-color: var(--vp-background-subtle) !important; }
.vp-bg-success { background-color: var(--vp-success-light) !important; }
.vp-bg-warning { background-color: var(--vp-warning-light) !important; }
.vp-bg-error { background-color: var(--vp-error-light) !important; }
.vp-bg-info { background-color: var(--vp-info-light) !important; }

/* Font Weights */
.vp-font-light { font-weight: var(--vp-font-weight-light) !important; }
.vp-font-normal { font-weight: var(--vp-font-weight-normal) !important; }
.vp-font-medium { font-weight: var(--vp-font-weight-medium) !important; }
.vp-font-semibold { font-weight: var(--vp-font-weight-semibold) !important; }
.vp-font-bold { font-weight: var(--vp-font-weight-bold) !important; }
.vp-font-extrabold { font-weight: var(--vp-font-weight-extrabold) !important; }

/* Font Sizes */
.vp-text-xs { font-size: var(--vp-font-size-xs) !important; }
.vp-text-sm { font-size: var(--vp-font-size-sm) !important; }
.vp-text-base { font-size: var(--vp-font-size-base) !important; }
.vp-text-lg { font-size: var(--vp-font-size-lg) !important; }
.vp-text-xl { font-size: var(--vp-font-size-xl) !important; }
.vp-text-2xl { font-size: var(--vp-font-size-2xl) !important; }
.vp-text-3xl { font-size: var(--vp-font-size-3xl) !important; }
.vp-text-4xl { font-size: var(--vp-font-size-4xl) !important; }
.vp-text-5xl { font-size: var(--vp-font-size-5xl) !important; }
.vp-text-6xl { font-size: var(--vp-font-size-6xl) !important; }

/* Spacing Utilities */
.vp-m-0 { margin: 0 !important; }
.vp-m-1 { margin: var(--vp-space-1) !important; }
.vp-m-2 { margin: var(--vp-space-2) !important; }
.vp-m-3 { margin: var(--vp-space-3) !important; }
.vp-m-4 { margin: var(--vp-space-4) !important; }
.vp-m-5 { margin: var(--vp-space-5) !important; }
.vp-m-6 { margin: var(--vp-space-6) !important; }
.vp-m-8 { margin: var(--vp-space-8) !important; }

.vp-mt-0 { margin-top: 0 !important; }
.vp-mt-1 { margin-top: var(--vp-space-1) !important; }
.vp-mt-2 { margin-top: var(--vp-space-2) !important; }
.vp-mt-3 { margin-top: var(--vp-space-3) !important; }
.vp-mt-4 { margin-top: var(--vp-space-4) !important; }
.vp-mt-5 { margin-top: var(--vp-space-5) !important; }
.vp-mt-6 { margin-top: var(--vp-space-6) !important; }
.vp-mt-8 { margin-top: var(--vp-space-8) !important; }

.vp-mb-0 { margin-bottom: 0 !important; }
.vp-mb-1 { margin-bottom: var(--vp-space-1) !important; }
.vp-mb-2 { margin-bottom: var(--vp-space-2) !important; }
.vp-mb-3 { margin-bottom: var(--vp-space-3) !important; }
.vp-mb-4 { margin-bottom: var(--vp-space-4) !important; }
.vp-mb-5 { margin-bottom: var(--vp-space-5) !important; }
.vp-mb-6 { margin-bottom: var(--vp-space-6) !important; }
.vp-mb-8 { margin-bottom: var(--vp-space-8) !important; }

.vp-p-0 { padding: 0 !important; }
.vp-p-1 { padding: var(--vp-space-1) !important; }
.vp-p-2 { padding: var(--vp-space-2) !important; }
.vp-p-3 { padding: var(--vp-space-3) !important; }
.vp-p-4 { padding: var(--vp-space-4) !important; }
.vp-p-5 { padding: var(--vp-space-5) !important; }
.vp-p-6 { padding: var(--vp-space-6) !important; }
.vp-p-8 { padding: var(--vp-space-8) !important; }

/* Border Radius */
.vp-rounded-none { border-radius: var(--vp-radius-none) !important; }
.vp-rounded-sm { border-radius: var(--vp-radius-sm) !important; }
.vp-rounded-md { border-radius: var(--vp-radius-md) !important; }
.vp-rounded-lg { border-radius: var(--vp-radius-lg) !important; }
.vp-rounded-xl { border-radius: var(--vp-radius-xl) !important; }
.vp-rounded-2xl { border-radius: var(--vp-radius-2xl) !important; }
.vp-rounded-full { border-radius: var(--vp-radius-full) !important; }

/* Shadows */
.vp-shadow-none { box-shadow: none !important; }
.vp-shadow-xs { box-shadow: var(--vp-shadow-xs) !important; }
.vp-shadow-sm { box-shadow: var(--vp-shadow-sm) !important; }
.vp-shadow-md { box-shadow: var(--vp-shadow-md) !important; }
.vp-shadow-lg { box-shadow: var(--vp-shadow-lg) !important; }
.vp-shadow-xl { box-shadow: var(--vp-shadow-xl) !important; }
.vp-shadow-2xl { box-shadow: var(--vp-shadow-2xl) !important; }

/* Borders */
.vp-border { border: 1px solid var(--vp-border) !important; }
.vp-border-light { border: 1px solid var(--vp-border-light) !important; }
.vp-border-dark { border: 1px solid var(--vp-border-dark) !important; }
.vp-border-primary { border: 1px solid var(--vp-primary) !important; }

.vp-border-t { border-top: 1px solid var(--vp-border) !important; }
.vp-border-b { border-bottom: 1px solid var(--vp-border) !important; }
.vp-border-l { border-left: 1px solid var(--vp-border) !important; }
.vp-border-r { border-right: 1px solid var(--vp-border) !important; }

/* Position */
.vp-relative { position: relative !important; }
.vp-absolute { position: absolute !important; }
.vp-fixed { position: fixed !important; }
.vp-sticky { position: sticky !important; }

/* Z-Index */
.vp-z-0 { z-index: 0 !important; }
.vp-z-10 { z-index: 10 !important; }
.vp-z-20 { z-index: 20 !important; }
.vp-z-30 { z-index: 30 !important; }
.vp-z-40 { z-index: 40 !important; }
.vp-z-50 { z-index: 50 !important; }

/* Overflow */
.vp-overflow-hidden { overflow: hidden !important; }
.vp-overflow-visible { overflow: visible !important; }
.vp-overflow-auto { overflow: auto !important; }
.vp-overflow-scroll { overflow: scroll !important; }

/* Width and Height */
.vp-w-full { width: 100% !important; }
.vp-w-auto { width: auto !important; }
.vp-h-full { height: 100% !important; }
.vp-h-auto { height: auto !important; }

/* Flex Utilities */
.vp-flex-1 { flex: 1 1 0% !important; }
.vp-flex-auto { flex: 1 1 auto !important; }
.vp-flex-none { flex: none !important; }

.vp-flex-col { flex-direction: column !important; }
.vp-flex-row { flex-direction: row !important; }

.vp-flex-wrap { flex-wrap: wrap !important; }
.vp-flex-nowrap { flex-wrap: nowrap !important; }

.vp-items-start { align-items: flex-start !important; }
.vp-items-end { align-items: flex-end !important; }
.vp-items-center { align-items: center !important; }
.vp-items-stretch { align-items: stretch !important; }

.vp-justify-start { justify-content: flex-start !important; }
.vp-justify-end { justify-content: flex-end !important; }
.vp-justify-center { justify-content: center !important; }
.vp-justify-between { justify-content: space-between !important; }
.vp-justify-around { justify-content: space-around !important; }

/* Grid Utilities */
.vp-grid-cols-1 { grid-template-columns: repeat(1, minmax(0, 1fr)) !important; }
.vp-grid-cols-2 { grid-template-columns: repeat(2, minmax(0, 1fr)) !important; }
.vp-grid-cols-3 { grid-template-columns: repeat(3, minmax(0, 1fr)) !important; }
.vp-grid-cols-4 { grid-template-columns: repeat(4, minmax(0, 1fr)) !important; }

.vp-gap-0 { gap: 0 !important; }
.vp-gap-1 { gap: var(--vp-space-1) !important; }
.vp-gap-2 { gap: var(--vp-space-2) !important; }
.vp-gap-3 { gap: var(--vp-space-3) !important; }
.vp-gap-4 { gap: var(--vp-space-4) !important; }
.vp-gap-5 { gap: var(--vp-space-5) !important; }
.vp-gap-6 { gap: var(--vp-space-6) !important; }
.vp-gap-8 { gap: var(--vp-space-8) !important; }

/* Transitions */
.vp-transition { transition: all var(--vp-transition-normal) !important; }
.vp-transition-fast { transition: all var(--vp-transition-fast) !important; }
.vp-transition-slow { transition: all var(--vp-transition-slow) !important; }

/* Transform */
.vp-transform { transform: translateZ(0) !important; }
.vp-scale-105 { transform: scale(1.05) !important; }
.vp-scale-110 { transform: scale(1.1) !important; }

/* Hover Effects */
.vp-hover-lift:hover {
    transform: translateY(-2px);
    box-shadow: var(--vp-shadow-lg);
}

.vp-hover-scale:hover {
    transform: scale(1.05);
}

/* Focus States */
.vp-focus-ring:focus {
    outline: none;
    box-shadow: 0 0 0 3px var(--vp-primary-light);
}

/* Responsive Utilities */
@media (min-width: 640px) {
    .sm\:vp-block { display: block !important; }
    .sm\:vp-hidden { display: none !important; }
    .sm\:vp-flex { display: flex !important; }
    .sm\:vp-grid { display: grid !important; }
    .sm\:vp-grid-cols-2 { grid-template-columns: repeat(2, minmax(0, 1fr)) !important; }
    .sm\:vp-grid-cols-3 { grid-template-columns: repeat(3, minmax(0, 1fr)) !important; }
}

@media (min-width: 768px) {
    .md\:vp-block { display: block !important; }
    .md\:vp-hidden { display: none !important; }
    .md\:vp-flex { display: flex !important; }
    .md\:vp-grid { display: grid !important; }
    .md\:vp-grid-cols-2 { grid-template-columns: repeat(2, minmax(0, 1fr)) !important; }
    .md\:vp-grid-cols-3 { grid-template-columns: repeat(3, minmax(0, 1fr)) !important; }
    .md\:vp-grid-cols-4 { grid-template-columns: repeat(4, minmax(0, 1fr)) !important; }
}

@media (min-width: 1024px) {
    .lg\:vp-block { display: block !important; }
    .lg\:vp-hidden { display: none !important; }
    .lg\:vp-flex { display: flex !important; }
    .lg\:vp-grid { display: grid !important; }
    .lg\:vp-grid-cols-3 { grid-template-columns: repeat(3, minmax(0, 1fr)) !important; }
    .lg\:vp-grid-cols-4 { grid-template-columns: repeat(4, minmax(0, 1fr)) !important; }
}
