<?php
/**
 * The template for displaying search results pages
 *
 * @package VlogPress
 * @since 1.0.0
 */

get_header();
?>

<main id="main" class="site-main search-results" role="main">
    <div class="vp-container">

        <header class="page-header search-header">
            <?php if ( have_posts() ) : ?>
                <h1 class="page-title">
                    <svg class="page-icon" width="32" height="32" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <circle cx="11" cy="11" r="8" stroke="currentColor" stroke-width="2"/>
                        <path d="m21 21-4.35-4.35" stroke="currentColor" stroke-width="2"/>
                    </svg>
                    <?php
                    printf(
                        /* translators: %s: search query. */
                        esc_html__( 'Search Results for: %s', 'vlogpress' ),
                        '<span class="search-query">' . get_search_query() . '</span>'
                    );
                    ?>
                </h1>

                <div class="search-results-count">
                    <?php
                    global $wp_query;
                    $total_results = $wp_query->found_posts;
                    printf(
                        /* translators: %s: number of search results. */
                        _n( '%s result found', '%s results found', $total_results, 'vlogpress' ),
                        '<strong>' . number_format_i18n( $total_results ) . '</strong>'
                    );
                    ?>
                </div>
            <?php else : ?>
                <h1 class="page-title">
                    <svg class="page-icon" width="32" height="32" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <circle cx="11" cy="11" r="8" stroke="currentColor" stroke-width="2"/>
                        <path d="m21 21-4.35-4.35" stroke="currentColor" stroke-width="2"/>
                    </svg>
                    <?php esc_html_e( 'Nothing Found', 'vlogpress' ); ?>
                </h1>
            <?php endif; ?>

            <!-- Search form for refinement -->
            <div class="search-form-container">
                <?php get_search_form(); ?>
            </div>
        </header>

        <?php if ( have_posts() ) : ?>

            <div class="search-results-grid">
                <?php while ( have_posts() ) : the_post(); ?>

                    <article id="post-<?php the_ID(); ?>" <?php post_class( 'search-result-item' ); ?>>

                        <?php if ( has_post_thumbnail() ) : ?>
                            <div class="search-result-thumbnail">
                                <a href="<?php the_permalink(); ?>" aria-hidden="true" tabindex="-1">
                                    <?php the_post_thumbnail( 'medium', array( 'alt' => the_title_attribute( array( 'echo' => false ) ) ) ); ?>
                                </a>

                                <?php if ( get_post_type() === 'vlog' ) : ?>
                                    <div class="vp-video-overlay">
                                        <svg class="vp-play-icon" width="32" height="32" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                            <path d="M8 5V19L19 12L8 5Z" fill="currentColor"/>
                                        </svg>
                                    </div>
                                <?php endif; ?>
                            </div>
                        <?php endif; ?>

                        <div class="search-result-content">
                            <header class="search-result-header">
                                <div class="search-result-meta">
                                    <span class="post-type-label <?php echo 'post-type-' . esc_attr( get_post_type() ); ?>">
                                        <?php if ( get_post_type() === 'vlog' ) : ?>
                                            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                <path d="M8 5V19L19 12L8 5Z" fill="currentColor"/>
                                            </svg>
                                            <?php _e( 'Video', 'vlogpress' ); ?>
                                        <?php else : ?>
                                            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z" stroke="currentColor" stroke-width="2" fill="none"/>
                                                <polyline points="14,2 14,8 20,8" stroke="currentColor" stroke-width="2" fill="none"/>
                                            </svg>
                                            <?php _e( 'Article', 'vlogpress' ); ?>
                                        <?php endif; ?>
                                    </span>

                                    <time class="published-date" datetime="<?php echo esc_attr( get_the_date( 'c' ) ); ?>">
                                        <?php echo esc_html( get_the_date() ); ?>
                                    </time>
                                </div>

                                <?php the_title( '<h2 class="search-result-title"><a href="' . esc_url( get_permalink() ) . '" rel="bookmark">', '</a></h2>' ); ?>
                            </header>

                            <div class="search-result-excerpt">
                                <?php
                                $excerpt = get_the_excerpt();
                                $search_query = get_search_query();

                                // Highlight search terms in excerpt
                                if ( $search_query && $excerpt ) {
                                    $highlighted_excerpt = preg_replace(
                                        '/(' . preg_quote( $search_query, '/' ) . ')/i',
                                        '<mark>$1</mark>',
                                        $excerpt
                                    );
                                    echo wp_kses_post( $highlighted_excerpt );
                                } else {
                                    the_excerpt();
                                }
                                ?>
                            </div>

                            <footer class="search-result-footer">
                                <a href="<?php the_permalink(); ?>" class="search-result-link">
                                    <?php if ( get_post_type() === 'vlog' ) : ?>
                                        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                            <path d="M8 5V19L19 12L8 5Z" fill="currentColor"/>
                                        </svg>
                                        <?php _e( 'Watch Video', 'vlogpress' ); ?>
                                    <?php else : ?>
                                        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                            <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z" stroke="currentColor" stroke-width="2" fill="none"/>
                                            <polyline points="14,2 14,8 20,8" stroke="currentColor" stroke-width="2" fill="none"/>
                                        </svg>
                                        <?php _e( 'Read More', 'vlogpress' ); ?>
                                    <?php endif; ?>
                                    <span class="screen-reader-text"><?php printf( __( 'about %s', 'vlogpress' ), get_the_title() ); ?></span>
                                </a>

                                <?php if ( has_category() ) : ?>
                                    <div class="search-result-categories">
                                        <?php the_category( ', ' ); ?>
                                    </div>
                                <?php endif; ?>
                            </footer>
                        </div>
                    </article>

                <?php endwhile; ?>
            </div>

            <?php
            // Pagination
            the_posts_pagination( array(
                'mid_size'  => 2,
                'prev_text' => __( '&larr; Previous', 'vlogpress' ),
                'next_text' => __( 'Next &rarr;', 'vlogpress' ),
                'class'     => 'search-pagination',
            ) );
            ?>

        <?php else : ?>

            <section class="no-results not-found">
                <div class="page-content">
                    <p><?php esc_html_e( 'Sorry, but nothing matched your search terms. Please try again with some different keywords.', 'vlogpress' ); ?></p>

                    <div class="search-suggestions">
                        <h3><?php esc_html_e( 'Search Suggestions:', 'vlogpress' ); ?></h3>
                        <ul>
                            <li><?php esc_html_e( 'Check your spelling', 'vlogpress' ); ?></li>
                            <li><?php esc_html_e( 'Try more general keywords', 'vlogpress' ); ?></li>
                            <li><?php esc_html_e( 'Try different keywords', 'vlogpress' ); ?></li>
                            <li><?php esc_html_e( 'Try fewer keywords', 'vlogpress' ); ?></li>
                        </ul>
                    </div>
                </div>
            </section>

        <?php endif; ?>

    </div>
</main>

<?php
get_sidebar();
get_footer();