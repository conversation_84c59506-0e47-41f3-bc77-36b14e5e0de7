<?php
/**
 * VlogPress functions and definitions
 *
 * @package VlogPress
 * @since 1.0.0
 */

// Prevent direct access
if ( ! defined( 'ABSPATH' ) ) {
    exit;
}

/**
 * Define theme constants
 */
define( 'VLOGPRESS_VERSION', '1.0.0' );
define( 'VLOGPRESS_THEME_DIR', get_template_directory() );
define( 'VLOGPRESS_THEME_URI', get_template_directory_uri() );

/**
 * Sets up theme defaults and registers support for various WordPress features.
 */
function vlogpress_setup() {
    // Make theme available for translation
    load_theme_textdomain( 'vlogpress', get_template_directory() . '/languages' );

    // Add default posts and comments RSS feed links to head
    add_theme_support( 'automatic-feed-links' );

    // Let WordPress manage the document title
    add_theme_support( 'title-tag' );

    // Enable support for Post Thumbnails on posts and pages
    add_theme_support( 'post-thumbnails' );

    // Add theme support for selective refresh for widgets
    add_theme_support( 'customize-selective-refresh-widgets' );

    // Add support for core custom logo
    add_theme_support( 'custom-logo', array(
        'height'      => 250,
        'width'       => 250,
        'flex-width'  => true,
        'flex-height' => true,
    ) );

    // Add support for HTML5 markup
    add_theme_support( 'html5', array(
        'search-form',
        'comment-form',
        'comment-list',
        'gallery',
        'caption',
        'style',
        'script',
    ) );

    // Set up the WordPress core custom background feature
    add_theme_support( 'custom-background', apply_filters( 'vlogpress_custom_background_args', array(
        'default-color' => 'ffffff',
        'default-image' => '',
    ) ) );

    // Add support for responsive embedded content
    add_theme_support( 'responsive-embeds' );

    // Add support for editor styles
    add_theme_support( 'editor-styles' );
    add_editor_style( 'css/editor-style.css' );

    // Add support for wide alignment
    add_theme_support( 'align-wide' );

    // Add support for block editor color palette
    add_theme_support( 'editor-color-palette', array(
        array(
            'name'  => __( 'Primary Blue', 'vlogpress' ),
            'slug'  => 'primary',
            'color' => '#2563eb',
        ),
        array(
            'name'  => __( 'Dark Blue', 'vlogpress' ),
            'slug'  => 'primary-dark',
            'color' => '#1e40af',
        ),
        array(
            'name'  => __( 'Light Blue', 'vlogpress' ),
            'slug'  => 'primary-light',
            'color' => '#dbeafe',
        ),
        array(
            'name'  => __( 'Dark Gray', 'vlogpress' ),
            'slug'  => 'text-primary',
            'color' => '#1f2937',
        ),
        array(
            'name'  => __( 'Medium Gray', 'vlogpress' ),
            'slug'  => 'text-secondary',
            'color' => '#6b7280',
        ),
        array(
            'name'  => __( 'Light Gray', 'vlogpress' ),
            'slug'  => 'background-alt',
            'color' => '#f3f4f6',
        ),
    ) );

    // Register navigation menus
    register_nav_menus( array(
        'primary' => __( 'Primary Menu', 'vlogpress' ),
        'footer'  => __( 'Footer Menu', 'vlogpress' ),
        'social'  => __( 'Social Links Menu', 'vlogpress' ),
    ) );

    // Add image sizes
    add_image_size( 'vlogpress-featured', 800, 450, true );
    add_image_size( 'vlogpress-thumbnail', 400, 225, true );
    add_image_size( 'vlogpress-small', 200, 113, true );
}
add_action( 'after_setup_theme', 'vlogpress_setup' );

/**
 * Set the content width in pixels, based on the theme's design and stylesheet.
 */
function vlogpress_content_width() {
    $GLOBALS['content_width'] = apply_filters( 'vlogpress_content_width', 800 );
}
add_action( 'after_setup_theme', 'vlogpress_content_width', 0 );

/**
 * Register widget area.
 */
function vlogpress_widgets_init() {
    register_sidebar( array(
        'name'          => __( 'Primary Sidebar', 'vlogpress' ),
        'id'            => 'sidebar-1',
        'description'   => __( 'Add widgets here to appear in your sidebar.', 'vlogpress' ),
        'before_widget' => '<section id="%1$s" class="widget %2$s">',
        'after_widget'  => '</section>',
        'before_title'  => '<h3 class="widget-title">',
        'after_title'   => '</h3>',
    ) );

    register_sidebar( array(
        'name'          => __( 'Footer Widget Area 1', 'vlogpress' ),
        'id'            => 'footer-1',
        'description'   => __( 'Add widgets here to appear in the first footer column.', 'vlogpress' ),
        'before_widget' => '<section id="%1$s" class="widget %2$s">',
        'after_widget'  => '</section>',
        'before_title'  => '<h3 class="widget-title">',
        'after_title'   => '</h3>',
    ) );

    register_sidebar( array(
        'name'          => __( 'Footer Widget Area 2', 'vlogpress' ),
        'id'            => 'footer-2',
        'description'   => __( 'Add widgets here to appear in the second footer column.', 'vlogpress' ),
        'before_widget' => '<section id="%1$s" class="widget %2$s">',
        'after_widget'  => '</section>',
        'before_title'  => '<h3 class="widget-title">',
        'after_title'   => '</h3>',
    ) );

    register_sidebar( array(
        'name'          => __( 'Footer Widget Area 3', 'vlogpress' ),
        'id'            => 'footer-3',
        'description'   => __( 'Add widgets here to appear in the third footer column.', 'vlogpress' ),
        'before_widget' => '<section id="%1$s" class="widget %2$s">',
        'after_widget'  => '</section>',
        'before_title'  => '<h3 class="widget-title">',
        'after_title'   => '</h3>',
    ) );
}
add_action( 'widgets_init', 'vlogpress_widgets_init' );

/**
 * Enqueue scripts and styles.
 */
function vlogpress_scripts() {
    // Enqueue main theme stylesheet
    wp_enqueue_style( 'vlogpress-style', get_stylesheet_uri(), array(), VLOGPRESS_VERSION );

    // Enqueue component stylesheets
    wp_enqueue_style( 'vlogpress-header', get_template_directory_uri() . '/css/components/header.css', array( 'vlogpress-style' ), VLOGPRESS_VERSION );
    wp_enqueue_style( 'vlogpress-footer', get_template_directory_uri() . '/css/components/footer.css', array( 'vlogpress-style' ), VLOGPRESS_VERSION );
    wp_enqueue_style( 'vlogpress-video', get_template_directory_uri() . '/css/components/video.css', array( 'vlogpress-style' ), VLOGPRESS_VERSION );
    wp_enqueue_style( 'vlogpress-pages', get_template_directory_uri() . '/css/components/pages.css', array( 'vlogpress-style' ), VLOGPRESS_VERSION );

    // Enqueue layout stylesheets
    wp_enqueue_style( 'vlogpress-layout', get_template_directory_uri() . '/css/layouts/main.css', array( 'vlogpress-style' ), VLOGPRESS_VERSION );

    // Enqueue utility stylesheets
    wp_enqueue_style( 'vlogpress-utilities', get_template_directory_uri() . '/css/utilities/helpers.css', array( 'vlogpress-style' ), VLOGPRESS_VERSION );

    // Enqueue theme JavaScript
    wp_enqueue_script( 'vlogpress-script', get_template_directory_uri() . '/js/theme.js', array(), VLOGPRESS_VERSION, true );

    // Enqueue comment reply script
    if ( is_singular() && comments_open() && get_option( 'thread_comments' ) ) {
        wp_enqueue_script( 'comment-reply' );
    }

    // Localize script for AJAX
    wp_localize_script( 'vlogpress-script', 'vlogpress_ajax', array(
        'ajax_url' => admin_url( 'admin-ajax.php' ),
        'nonce'    => wp_create_nonce( 'vlogpress_nonce' ),
    ) );
}
add_action( 'wp_enqueue_scripts', 'vlogpress_scripts' );

/**
 * Custom query functions for separate vlog and blog pages
 */

/**
 * Get vlogs with optional category filter
 */
function vlogpress_get_vlogs( $args = array() ) {
    $default_args = array(
        'post_type'      => 'vlog',
        'post_status'    => 'publish',
        'posts_per_page' => get_option( 'posts_per_page' ),
        'meta_query'     => array(
            'relation' => 'OR',
            array(
                'key'     => '_vp_video_url',
                'compare' => 'EXISTS'
            ),
            array(
                'key'     => '_vp_video_url',
                'value'   => '',
                'compare' => '!='
            )
        )
    );

    $args = wp_parse_args( $args, $default_args );
    return new WP_Query( $args );
}

/**
 * Get blog posts with optional category filter
 */
function vlogpress_get_blogs( $args = array() ) {
    $default_args = array(
        'post_type'      => 'post',
        'post_status'    => 'publish',
        'posts_per_page' => get_option( 'posts_per_page' ),
    );

    $args = wp_parse_args( $args, $default_args );
    return new WP_Query( $args );
}

/**
 * Get content count by type
 */
function vlogpress_get_content_count( $post_type = 'post' ) {
    $count = wp_count_posts( $post_type );
    return $count->publish;
}

/**
 * Check if current page is vlogs or blogs page
 */
function vlogpress_is_vlogs_page() {
    global $post;
    return ( is_page() && $post && $post->post_name === 'vlogs' ) ||
           ( is_page_template( 'page-vlogs.php' ) );
}

function vlogpress_is_blogs_page() {
    global $post;
    return ( is_page() && $post && $post->post_name === 'blogs' ) ||
           ( is_page_template( 'page-blogs.php' ) );
}



/**
 * Modify page title for vlog and blog pages
 */
function vlogpress_page_title( $title ) {
    if ( vlogpress_is_vlogs_page() ) {
        return __( 'Video Content', 'vlogpress' ) . ' | ' . get_bloginfo( 'name' );
    }

    if ( vlogpress_is_blogs_page() ) {
        return __( 'Blog Articles', 'vlogpress' ) . ' | ' . get_bloginfo( 'name' );
    }

    return $title;
}
add_filter( 'pre_get_document_title', 'vlogpress_page_title' );

/**
 * Add rewrite rules for vlogs and blogs pages
 */
function vlogpress_add_rewrite_rules() {
    add_rewrite_rule( '^vlogs/?$', 'index.php?pagename=vlogs', 'top' );
    add_rewrite_rule( '^vlogs/page/([0-9]+)/?$', 'index.php?pagename=vlogs&paged=$matches[1]', 'top' );
    add_rewrite_rule( '^blogs/?$', 'index.php?pagename=blogs', 'top' );
    add_rewrite_rule( '^blogs/page/([0-9]+)/?$', 'index.php?pagename=blogs&paged=$matches[1]', 'top' );
}
add_action( 'init', 'vlogpress_add_rewrite_rules' );

/**
 * Create vlogs and blogs pages on theme activation
 */
function vlogpress_create_archive_pages() {
    // Create Vlogs page
    $vlogs_page = get_page_by_path( 'vlogs' );
    if ( ! $vlogs_page ) {
        $vlogs_page_id = wp_insert_post( array(
            'post_title'     => __( 'Videos', 'vlogpress' ),
            'post_name'      => 'vlogs',
            'post_content'   => __( 'Discover our latest video content, tutorials, and vlogs.', 'vlogpress' ),
            'post_status'    => 'publish',
            'post_type'      => 'page',
            'page_template'  => 'page-vlogs.php',
            'meta_input'     => array(
                '_wp_page_template' => 'page-vlogs.php'
            )
        ) );
    }

    // Create Blogs page
    $blogs_page = get_page_by_path( 'blogs' );
    if ( ! $blogs_page ) {
        $blogs_page_id = wp_insert_post( array(
            'post_title'     => __( 'Blog', 'vlogpress' ),
            'post_name'      => 'blogs',
            'post_content'   => __( 'Read our latest articles, insights, and written content.', 'vlogpress' ),
            'post_status'    => 'publish',
            'post_type'      => 'page',
            'page_template'  => 'page-blogs.php',
            'meta_input'     => array(
                '_wp_page_template' => 'page-blogs.php'
            )
        ) );
    }
}

/**
 * Flush rewrite rules on theme activation
 */
function vlogpress_flush_rewrite_rules() {
    vlogpress_create_archive_pages();
    vlogpress_add_rewrite_rules();
    flush_rewrite_rules();
}
add_action( 'after_switch_theme', 'vlogpress_flush_rewrite_rules' );

/**
 * Add admin notice for created pages
 */
function vlogpress_admin_notice_pages_created() {
    $screen = get_current_screen();
    if ( $screen->id !== 'themes' ) {
        return;
    }

    $vlogs_page = get_page_by_path( 'vlogs' );
    $blogs_page = get_page_by_path( 'blogs' );

    if ( $vlogs_page && $blogs_page ) {
        echo '<div class="notice notice-success is-dismissible">';
        echo '<p><strong>' . __( 'VlogPress Theme Activated!', 'vlogpress' ) . '</strong></p>';
        echo '<p>' . sprintf(
            __( 'Separate pages for <a href="%1$s">Videos</a> and <a href="%2$s">Blog</a> have been created automatically.', 'vlogpress' ),
            get_permalink( $vlogs_page->ID ),
            get_permalink( $blogs_page->ID )
        ) . '</p>';
        echo '</div>';
    }
}
add_action( 'admin_notices', 'vlogpress_admin_notice_pages_created' );

/**
 * Custom template tags for this theme.
 */
require get_template_directory() . '/inc/template-tags.php';

/**
 * Functions which enhance the theme by hooking into WordPress.
 */
require get_template_directory() . '/inc/template-functions.php';

/**
 * Customizer additions.
 */
require get_template_directory() . '/inc/customizer.php';

/**
 * Custom post types.
 */
require get_template_directory() . '/inc/post-types.php';

/**
 * Helper function to get reading time
 */
function vp_get_reading_time( $post_id = null ) {
    if ( ! $post_id ) {
        $post_id = get_the_ID();
    }

    $content = get_post_field( 'post_content', $post_id );
    $word_count = str_word_count( strip_tags( $content ) );
    $reading_time = ceil( $word_count / 200 ); // Average reading speed: 200 words per minute

    return sprintf( _n( '%d min read', '%d min read', $reading_time, 'vlogpress' ), $reading_time );
}

/**
 * Helper function to get video duration in human readable format
 */
function vp_format_video_duration( $seconds ) {
    if ( $seconds < 60 ) {
        return sprintf( '%d sec', $seconds );
    } elseif ( $seconds < 3600 ) {
        $minutes = floor( $seconds / 60 );
        $remaining_seconds = $seconds % 60;
        return sprintf( '%d:%02d', $minutes, $remaining_seconds );
    } else {
        $hours = floor( $seconds / 3600 );
        $minutes = floor( ( $seconds % 3600 ) / 60 );
        $remaining_seconds = $seconds % 60;
        return sprintf( '%d:%02d:%02d', $hours, $minutes, $remaining_seconds );
    }
}

/**
 * Add custom body classes
 */
function vlogpress_body_classes( $classes ) {
    // Add class for the current post type
    if ( is_singular() ) {
        $classes[] = 'single-' . get_post_type();
    }

    // Add class for sidebar
    if ( is_active_sidebar( 'sidebar-1' ) ) {
        $classes[] = 'has-sidebar';
    } else {
        $classes[] = 'no-sidebar';
    }

    // Add classes for vlog and blog pages
    if ( vlogpress_is_vlogs_page() ) {
        $classes[] = 'vlogs-page';
        $classes[] = 'content-archive';
    }

    if ( vlogpress_is_blogs_page() ) {
        $classes[] = 'blogs-page';
        $classes[] = 'content-archive';
    }

    return $classes;
}
add_filter( 'body_class', 'vlogpress_body_classes' );

/**
 * Add a pingback url auto-discovery header for single posts, pages, or attachments.
 */
function vlogpress_pingback_header() {
    if ( is_singular() && pings_open() ) {
        printf( '<link rel="pingback" href="%s">', esc_url( get_bloginfo( 'pingback_url' ) ) );
    }
}
add_action( 'wp_head', 'vlogpress_pingback_header' );

/**
 * Implement the Custom Header feature.
 */
require get_template_directory() . '/inc/custom-header.php';

/**
 * Load Jetpack compatibility file.
 */
if ( defined( 'JETPACK__VERSION' ) ) {
    require get_template_directory() . '/inc/jetpack.php';
}