<?php
/**
 * Template for displaying search forms
 *
 * @package VlogPress
 * @since 1.0.0
 */

$vlogpress_unique_id = wp_unique_id( 'search-form-' );
$vlogpress_aria_label = ! empty( $args['aria_label'] ) ? 'aria-label="' . esc_attr( $args['aria_label'] ) . '"' : '';
?>

<form role="search" <?php echo $vlogpress_aria_label; // phpcs:ignore WordPress.Security.EscapeOutput.OutputNotEscaped ?> method="get" class="search-form" action="<?php echo esc_url( home_url( '/' ) ); ?>">
    <div class="search-form-wrapper">
        <label for="<?php echo esc_attr( $vlogpress_unique_id ); ?>" class="search-label">
            <span class="screen-reader-text"><?php _e( 'Search for:', 'vlogpress' ); ?></span>
        </label>
        
        <div class="search-input-wrapper">
            <input 
                type="search" 
                id="<?php echo esc_attr( $vlogpress_unique_id ); ?>" 
                class="search-field" 
                placeholder="<?php echo esc_attr_x( 'Search...', 'placeholder', 'vlogpress' ); ?>" 
                value="<?php echo get_search_query(); ?>" 
                name="s" 
                required
                autocomplete="off"
                aria-describedby="<?php echo esc_attr( $vlogpress_unique_id ); ?>-description"
            />
            
            <button type="submit" class="search-submit" aria-label="<?php esc_attr_e( 'Search', 'vlogpress' ); ?>">
                <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" aria-hidden="true">
                    <circle cx="11" cy="11" r="8" stroke="currentColor" stroke-width="2"/>
                    <path d="m21 21-4.35-4.35" stroke="currentColor" stroke-width="2"/>
                </svg>
                <span class="search-submit-text"><?php _e( 'Search', 'vlogpress' ); ?></span>
            </button>
        </div>
        
        <div id="<?php echo esc_attr( $vlogpress_unique_id ); ?>-description" class="search-description screen-reader-text">
            <?php _e( 'Press Enter to search or ESC to close', 'vlogpress' ); ?>
        </div>
    </div>
</form>
