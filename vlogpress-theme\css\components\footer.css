/* ==========================================================================
   Footer Components - Professional Fortune 500 Style
   ========================================================================== */

/* Site Footer */
.site-footer {
    background: linear-gradient(135deg, var(--vp-secondary) 0%, var(--vp-text-primary) 100%);
    color: var(--vp-text-inverse);
    margin-top: auto;
}

.footer-widgets {
    padding: var(--vp-space-16) 0 var(--vp-space-12);
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.footer-widgets .vp-container {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: var(--vp-space-10);
}

.footer-widget-area {
    display: flex;
    flex-direction: column;
}

/* Footer Widget Styles */
.footer-widget-area .widget {
    margin-bottom: var(--vp-space-8);
}

.footer-widget-area .widget:last-child {
    margin-bottom: 0;
}

.footer-widget-area .widget-title {
    color: var(--vp-text-inverse);
    font-size: var(--vp-font-size-lg);
    font-weight: var(--vp-font-weight-semibold);
    margin-bottom: var(--vp-space-4);
    position: relative;
    padding-bottom: var(--vp-space-2);
}

.footer-widget-area .widget-title::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 40px;
    height: 2px;
    background-color: var(--vp-primary);
    border-radius: 1px;
}

.footer-widget-area p {
    color: rgba(255, 255, 255, 0.8);
    line-height: var(--vp-line-height-relaxed);
    margin-bottom: var(--vp-space-4);
}

.footer-widget-area a {
    color: rgba(255, 255, 255, 0.9);
    text-decoration: none;
    transition: color var(--vp-transition-fast);
}

.footer-widget-area a:hover {
    color: var(--vp-text-inverse);
}

/* Footer Lists */
.footer-widget-area ul {
    list-style: none;
    padding: 0;
    margin: 0;
}

.footer-widget-area li {
    margin-bottom: var(--vp-space-2);
    color: rgba(255, 255, 255, 0.8);
}

.footer-widget-area li:last-child {
    margin-bottom: 0;
}

.footer-widget-area li a {
    display: flex;
    align-items: center;
    gap: var(--vp-space-2);
    padding: var(--vp-space-1) 0;
    border-radius: var(--vp-radius-sm);
    transition: all var(--vp-transition-fast);
}

.footer-widget-area li a:hover {
    color: var(--vp-text-inverse);
    padding-left: var(--vp-space-2);
}

/* Footer Info Section */
.footer-info {
    padding: var(--vp-space-8) 0;
}

.footer-info .vp-container {
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: var(--vp-space-6);
    flex-wrap: wrap;
}

.footer-branding {
    display: flex;
    align-items: center;
    gap: var(--vp-space-4);
}

.footer-logo {
    height: 40px;
    width: auto;
    opacity: 0.9;
}

.footer-text {
    color: rgba(255, 255, 255, 0.7);
    font-size: var(--vp-font-size-sm);
    margin: 0;
}

.footer-menu {
    display: flex;
    align-items: center;
    list-style: none;
    margin: 0;
    padding: 0;
    gap: var(--vp-space-6);
}

.footer-menu a {
    color: rgba(255, 255, 255, 0.8);
    text-decoration: none;
    font-size: var(--vp-font-size-sm);
    font-weight: var(--vp-font-weight-medium);
    transition: color var(--vp-transition-fast);
}

.footer-menu a:hover {
    color: var(--vp-text-inverse);
}

/* Footer Social Links */
.footer-social {
    display: flex;
    align-items: center;
    gap: var(--vp-space-3);
}

.footer-social a {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 44px;
    height: 44px;
    background-color: rgba(255, 255, 255, 0.1);
    color: rgba(255, 255, 255, 0.8);
    border-radius: var(--vp-radius-lg);
    transition: all var(--vp-transition-fast);
}

.footer-social a:hover {
    background-color: var(--vp-primary);
    color: var(--vp-text-inverse);
    transform: translateY(-2px);
}

/* Copyright Section */
.footer-copyright {
    background-color: rgba(0, 0, 0, 0.2);
    padding: var(--vp-space-4) 0;
    text-align: center;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.footer-copyright p {
    margin: 0;
    color: rgba(255, 255, 255, 0.6);
    font-size: var(--vp-font-size-sm);
}

.footer-copyright a {
    color: rgba(255, 255, 255, 0.8);
    text-decoration: none;
    transition: color var(--vp-transition-fast);
}

.footer-copyright a:hover {
    color: var(--vp-text-inverse);
}

/* Newsletter Signup Widget */
.widget_newsletter_signup {
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.05) 100%);
    padding: var(--vp-space-6);
    border-radius: var(--vp-radius-xl);
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.newsletter-form {
    display: flex;
    gap: var(--vp-space-2);
    margin-top: var(--vp-space-4);
}

.newsletter-form input[type="email"] {
    flex: 1;
    padding: var(--vp-space-3) var(--vp-space-4);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: var(--vp-radius-lg);
    background-color: rgba(255, 255, 255, 0.1);
    color: var(--vp-text-inverse);
    font-size: var(--vp-font-size-sm);
    transition: all var(--vp-transition-fast);
}

.newsletter-form input[type="email"]::placeholder {
    color: rgba(255, 255, 255, 0.6);
}

.newsletter-form input[type="email"]:focus {
    outline: none;
    border-color: var(--vp-primary);
    background-color: rgba(255, 255, 255, 0.15);
}

.newsletter-form button {
    padding: var(--vp-space-3) var(--vp-space-5);
    background-color: var(--vp-primary);
    color: var(--vp-text-inverse);
    border: none;
    border-radius: var(--vp-radius-lg);
    font-weight: var(--vp-font-weight-medium);
    font-size: var(--vp-font-size-sm);
    cursor: pointer;
    transition: all var(--vp-transition-fast);
    white-space: nowrap;
}

.newsletter-form button:hover {
    background-color: var(--vp-primary-hover);
    transform: translateY(-1px);
}

/* Contact Info Widget */
.widget_contact_info {
    background: rgba(255, 255, 255, 0.05);
    padding: var(--vp-space-6);
    border-radius: var(--vp-radius-xl);
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.contact-info-list {
    list-style: none;
    padding: 0;
    margin: var(--vp-space-4) 0 0;
}

.contact-info-item {
    display: flex;
    align-items: center;
    gap: var(--vp-space-3);
    margin-bottom: var(--vp-space-3);
    color: rgba(255, 255, 255, 0.9);
}

.contact-info-item:last-child {
    margin-bottom: 0;
}

.contact-info-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 24px;
    height: 24px;
    color: var(--vp-primary);
    flex-shrink: 0;
}

/* Responsive Design */
@media (max-width: 768px) {
    .footer-widgets {
        padding: var(--vp-space-12) 0 var(--vp-space-8);
    }
    
    .footer-widgets .vp-container {
        grid-template-columns: 1fr;
        gap: var(--vp-space-8);
    }
    
    .footer-info .vp-container {
        flex-direction: column;
        text-align: center;
        gap: var(--vp-space-4);
    }
    
    .footer-menu {
        flex-wrap: wrap;
        justify-content: center;
        gap: var(--vp-space-4);
    }
    
    .newsletter-form {
        flex-direction: column;
    }
    
    .newsletter-form button {
        align-self: stretch;
    }
}

@media (max-width: 480px) {
    .footer-widgets {
        padding: var(--vp-space-8) 0 var(--vp-space-6);
    }
    
    .footer-social {
        justify-content: center;
        flex-wrap: wrap;
    }
    
    .widget_newsletter_signup,
    .widget_contact_info {
        padding: var(--vp-space-4);
    }
}
