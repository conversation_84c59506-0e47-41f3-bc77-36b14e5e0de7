/**
 * VlogPress Theme JavaScript - Professional Fortune 500 Edition
 *
 * @package VlogPress
 * @since 1.0.0
 */

(function() {
    'use strict';

    // Mobile menu toggle with smooth animations
    function initMobileMenu() {
        const menuToggle = document.querySelector('.menu-toggle');
        const navigation = document.querySelector('.main-navigation');
        const primaryMenu = document.querySelector('.primary-menu');

        if (menuToggle && navigation && primaryMenu) {
            menuToggle.addEventListener('click', function(e) {
                e.preventDefault();
                const expanded = this.getAttribute('aria-expanded') === 'true';

                this.setAttribute('aria-expanded', !expanded);
                navigation.classList.toggle('active');

                // Add smooth animation
                if (!expanded) {
                    primaryMenu.style.display = 'flex';
                    setTimeout(() => {
                        primaryMenu.style.opacity = '1';
                        primaryMenu.style.visibility = 'visible';
                        primaryMenu.style.transform = 'translateY(0)';
                    }, 10);
                } else {
                    primaryMenu.style.opacity = '0';
                    primaryMenu.style.visibility = 'hidden';
                    primaryMenu.style.transform = 'translateY(-10px)';
                    setTimeout(() => {
                        if (!navigation.classList.contains('active')) {
                            primaryMenu.style.display = 'none';
                        }
                    }, 200);
                }
            });

            // Close menu when clicking outside
            document.addEventListener('click', function(e) {
                if (!navigation.contains(e.target) && navigation.classList.contains('active')) {
                    menuToggle.setAttribute('aria-expanded', 'false');
                    navigation.classList.remove('active');
                    primaryMenu.style.opacity = '0';
                    primaryMenu.style.visibility = 'hidden';
                    primaryMenu.style.transform = 'translateY(-10px)';
                    setTimeout(() => {
                        primaryMenu.style.display = 'none';
                    }, 200);
                }
            });

            // Close menu on escape key
            document.addEventListener('keydown', function(e) {
                if (e.key === 'Escape' && navigation.classList.contains('active')) {
                    menuToggle.click();
                }
            });
        }
    }

    // Enhanced search toggle with animations
    function initSearchToggle() {
        const searchToggle = document.querySelector('.search-toggle-button');
        const searchFormContainer = document.querySelector('.search-form-container');

        if (searchToggle && searchFormContainer) {
            searchToggle.addEventListener('click', function(e) {
                e.preventDefault();
                const expanded = this.getAttribute('aria-expanded') === 'true';

                this.setAttribute('aria-expanded', !expanded);
                searchFormContainer.classList.toggle('active');

                if (!expanded) {
                    const searchInput = searchFormContainer.querySelector('input[type="search"]');
                    if (searchInput) {
                        setTimeout(() => searchInput.focus(), 150);
                    }
                }
            });

            // Close search when clicking outside
            document.addEventListener('click', function(e) {
                const searchToggleContainer = document.querySelector('.search-toggle');
                if (!searchToggleContainer.contains(e.target) && searchFormContainer.classList.contains('active')) {
                    searchToggle.setAttribute('aria-expanded', 'false');
                    searchFormContainer.classList.remove('active');
                }
            });

            // Close search on escape key
            document.addEventListener('keydown', function(e) {
                if (e.key === 'Escape' && searchFormContainer.classList.contains('active')) {
                    searchToggle.click();
                }
            });
        }
    }

    // Professional smooth scrolling with offset for fixed header
    function initSmoothScrolling() {
        const links = document.querySelectorAll('a[href^="#"]');
        const header = document.querySelector('.site-header');
        const headerHeight = header ? header.offsetHeight : 0;

        links.forEach(link => {
            link.addEventListener('click', function(e) {
                const href = this.getAttribute('href');
                if (href === '#' || href === '#top') return;

                const target = document.querySelector(href);
                if (target) {
                    e.preventDefault();
                    const targetPosition = target.getBoundingClientRect().top + window.pageYOffset - headerHeight - 20;

                    window.scrollTo({
                        top: targetPosition,
                        behavior: 'smooth'
                    });
                }
            });
        });
    }

    // Enhanced video lazy loading with fade-in effect
    function initVideoLazyLoading() {
        const videos = document.querySelectorAll('iframe[data-src], img[data-src]');

        if ('IntersectionObserver' in window) {
            const mediaObserver = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        const media = entry.target;
                        media.src = media.dataset.src;
                        media.removeAttribute('data-src');
                        media.classList.add('loaded');
                        mediaObserver.unobserve(media);
                    }
                });
            }, {
                rootMargin: '50px 0px',
                threshold: 0.1
            });

            videos.forEach(media => {
                mediaObserver.observe(media);
            });
        } else {
            // Fallback for browsers without IntersectionObserver
            videos.forEach(media => {
                media.src = media.dataset.src;
                media.removeAttribute('data-src');
                media.classList.add('loaded');
            });
        }
    }

    // Professional card hover effects
    function initCardEffects() {
        const cards = document.querySelectorAll('.vp-content-card');

        cards.forEach(card => {
            card.addEventListener('mouseenter', function() {
                this.style.transform = 'translateY(-4px)';
            });

            card.addEventListener('mouseleave', function() {
                this.style.transform = 'translateY(0)';
            });
        });
    }

    // Sticky header with scroll effects
    function initStickyHeader() {
        const header = document.querySelector('.site-header');
        if (!header) return;

        let lastScrollTop = 0;
        let ticking = false;

        function updateHeader() {
            const scrollTop = window.pageYOffset || document.documentElement.scrollTop;

            if (scrollTop > 100) {
                header.classList.add('scrolled');
            } else {
                header.classList.remove('scrolled');
            }

            // Hide header when scrolling down, show when scrolling up
            if (scrollTop > lastScrollTop && scrollTop > 200) {
                header.style.transform = 'translateY(-100%)';
            } else {
                header.style.transform = 'translateY(0)';
            }

            lastScrollTop = scrollTop;
            ticking = false;
        }

        function requestTick() {
            if (!ticking) {
                requestAnimationFrame(updateHeader);
                ticking = true;
            }
        }

        window.addEventListener('scroll', requestTick);
    }

    // Form enhancements
    function initFormEnhancements() {
        const forms = document.querySelectorAll('form');

        forms.forEach(form => {
            const inputs = form.querySelectorAll('input, textarea, select');

            inputs.forEach(input => {
                // Add focus/blur effects
                input.addEventListener('focus', function() {
                    this.parentElement.classList.add('focused');
                });

                input.addEventListener('blur', function() {
                    this.parentElement.classList.remove('focused');
                    if (this.value) {
                        this.parentElement.classList.add('has-value');
                    } else {
                        this.parentElement.classList.remove('has-value');
                    }
                });

                // Check initial state
                if (input.value) {
                    input.parentElement.classList.add('has-value');
                }
            });
        });
    }

    // Accessibility improvements
    function initAccessibility() {
        // Skip link functionality
        const skipLink = document.querySelector('.vp-skip-link, .skip-link');
        if (skipLink) {
            skipLink.addEventListener('click', function(e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.focus();
                    target.scrollIntoView();
                }
            });
        }

        // Keyboard navigation for dropdowns
        const menuItems = document.querySelectorAll('.primary-menu a');
        menuItems.forEach(item => {
            item.addEventListener('keydown', function(e) {
                if (e.key === 'Enter' || e.key === ' ') {
                    this.click();
                }
            });
        });
    }

    // Performance optimization: Debounce function
    function debounce(func, wait, immediate) {
        let timeout;
        return function executedFunction() {
            const context = this;
            const args = arguments;
            const later = function() {
                timeout = null;
                if (!immediate) func.apply(context, args);
            };
            const callNow = immediate && !timeout;
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
            if (callNow) func.apply(context, args);
        };
    }

    // Content filtering for vlogs and blogs pages
    function initContentFiltering() {
        const filterButtons = document.querySelectorAll('.filter-btn');
        const contentCards = document.querySelectorAll('.vp-content-card[data-categories]');

        if (filterButtons.length === 0 || contentCards.length === 0) return;

        filterButtons.forEach(button => {
            button.addEventListener('click', function() {
                const filter = this.getAttribute('data-filter');

                // Update active button
                filterButtons.forEach(btn => btn.classList.remove('active'));
                this.classList.add('active');

                // Filter content cards
                contentCards.forEach(card => {
                    const categories = card.getAttribute('data-categories');

                    if (filter === 'all' || categories.includes(filter)) {
                        card.style.display = 'block';
                        card.style.opacity = '0';
                        card.style.transform = 'translateY(20px)';

                        setTimeout(() => {
                            card.style.opacity = '1';
                            card.style.transform = 'translateY(0)';
                        }, 100);
                    } else {
                        card.style.opacity = '0';
                        card.style.transform = 'translateY(-20px)';

                        setTimeout(() => {
                            card.style.display = 'none';
                        }, 200);
                    }
                });

                // Update URL without page reload
                const url = new URL(window.location);
                if (filter === 'all') {
                    url.searchParams.delete('filter');
                } else {
                    url.searchParams.set('filter', filter);
                }
                window.history.replaceState({}, '', url);
            });
        });

        // Apply filter from URL on page load
        const urlParams = new URLSearchParams(window.location.search);
        const urlFilter = urlParams.get('filter');
        if (urlFilter) {
            const filterButton = document.querySelector(`[data-filter="${urlFilter}"]`);
            if (filterButton) {
                filterButton.click();
            }
        }
    }

    // Enhanced card animations for archive pages
    function initArchiveAnimations() {
        const cards = document.querySelectorAll('.vlogs-page .vp-content-card, .blogs-page .vp-content-card');

        if ('IntersectionObserver' in window && cards.length > 0) {
            const cardObserver = new IntersectionObserver((entries) => {
                entries.forEach((entry, index) => {
                    if (entry.isIntersecting) {
                        setTimeout(() => {
                            entry.target.style.opacity = '1';
                            entry.target.style.transform = 'translateY(0)';
                        }, index * 100);
                        cardObserver.unobserve(entry.target);
                    }
                });
            }, {
                threshold: 0.1,
                rootMargin: '50px 0px'
            });

            cards.forEach(card => {
                card.style.opacity = '0';
                card.style.transform = 'translateY(30px)';
                card.style.transition = 'opacity 0.6s ease, transform 0.6s ease';
                cardObserver.observe(card);
            });
        }
    }

    // Initialize all functions when DOM is ready
    function init() {
        initMobileMenu();
        initSearchToggle();
        initSmoothScrolling();
        initVideoLazyLoading();
        initCardEffects();
        initStickyHeader();
        initFormEnhancements();
        initAccessibility();
        initContentFiltering();
        initArchiveAnimations();

        // Add loaded class to body for CSS animations
        document.body.classList.add('loaded');
    }

    // Run when DOM is ready
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', init);
    } else {
        init();
    }

})();