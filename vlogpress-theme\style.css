/*
Theme Name: VlogPress
Description: A modern, responsive WordPress theme that seamlessly combines video blogging (vlogging) and traditional text blogging capabilities. Perfect for content creators, influencers, and businesses who create both video and written content.
Author: VlogPress Team
Version: 1.0.0
Requires at least: 5.0
Tested up to: 6.4
Requires PHP: 7.4
License: GPL v2 or later
License URI: https://www.gnu.org/licenses/gpl-2.0.html
Text Domain: vlogpress
Tags: blog, video, vlog, responsive, custom-post-types, featured-images, threaded-comments, translation-ready, accessibility-ready
*/

/* ==========================================================================
   CSS Reset & Base Styles
   ========================================================================== */

*,
*::before,
*::after {
    box-sizing: border-box;
}

html {
    font-size: 16px;
    line-height: 1.6;
    -webkit-text-size-adjust: 100%;
    -ms-text-size-adjust: 100%;
}

body {
    margin: 0;
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
    font-size: 1rem;
    line-height: 1.7;
    color: #1a202c;
    background-color: #ffffff;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    font-feature-settings: 'kern' 1, 'liga' 1, 'calt' 1;
    text-rendering: optimizeLegibility;
}

/* ==========================================================================
   CSS Custom Properties (Variables)
   ========================================================================== */

:root {
    /* Colors - Professional Fortune 500 Palette */
    --vp-primary: #0066cc;
    --vp-primary-dark: #004499;
    --vp-primary-light: #e6f2ff;
    --vp-primary-hover: #0052a3;
    --vp-secondary: #2d3748;
    --vp-secondary-light: #4a5568;
    --vp-accent: #ed8936;
    --vp-accent-light: #fef5e7;

    --vp-text-primary: #1a202c;
    --vp-text-secondary: #4a5568;
    --vp-text-tertiary: #718096;
    --vp-text-light: #a0aec0;
    --vp-text-inverse: #ffffff;

    --vp-background: #ffffff;
    --vp-background-alt: #f7fafc;
    --vp-background-subtle: #edf2f7;
    --vp-background-overlay: rgba(26, 32, 44, 0.6);

    --vp-border: #e2e8f0;
    --vp-border-light: #f1f5f9;
    --vp-border-dark: #cbd5e0;

    --vp-success: #38a169;
    --vp-success-light: #f0fff4;
    --vp-warning: #d69e2e;
    --vp-warning-light: #fffbeb;
    --vp-error: #e53e3e;
    --vp-error-light: #fed7d7;
    --vp-info: #3182ce;
    --vp-info-light: #ebf8ff;
    --vp-video: #e53e3e;

    /* Typography - Professional System */
    --vp-font-family-primary: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
    --vp-font-family-heading: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
    --vp-font-family-mono: 'SF Mono', Monaco, 'Cascadia Code', 'Roboto Mono', Consolas, 'Courier New', monospace;

    --vp-font-size-xs: 0.75rem;      /* 12px */
    --vp-font-size-sm: 0.875rem;     /* 14px */
    --vp-font-size-base: 1rem;       /* 16px */
    --vp-font-size-lg: 1.125rem;     /* 18px */
    --vp-font-size-xl: 1.25rem;      /* 20px */
    --vp-font-size-2xl: 1.5rem;      /* 24px */
    --vp-font-size-3xl: 1.875rem;    /* 30px */
    --vp-font-size-4xl: 2.25rem;     /* 36px */
    --vp-font-size-5xl: 3rem;        /* 48px */
    --vp-font-size-6xl: 3.75rem;     /* 60px */

    /* Line Heights - Optimized for readability */
    --vp-line-height-none: 1;
    --vp-line-height-tight: 1.25;
    --vp-line-height-snug: 1.375;
    --vp-line-height-normal: 1.5;
    --vp-line-height-relaxed: 1.625;
    --vp-line-height-loose: 2;

    /* Font Weights */
    --vp-font-weight-light: 300;
    --vp-font-weight-normal: 400;
    --vp-font-weight-medium: 500;
    --vp-font-weight-semibold: 600;
    --vp-font-weight-bold: 700;
    --vp-font-weight-extrabold: 800;

    /* Spacing - Consistent 8px grid system */
    --vp-space-0: 0;
    --vp-space-px: 1px;
    --vp-space-0-5: 0.125rem;    /* 2px */
    --vp-space-1: 0.25rem;       /* 4px */
    --vp-space-1-5: 0.375rem;    /* 6px */
    --vp-space-2: 0.5rem;        /* 8px */
    --vp-space-2-5: 0.625rem;    /* 10px */
    --vp-space-3: 0.75rem;       /* 12px */
    --vp-space-3-5: 0.875rem;    /* 14px */
    --vp-space-4: 1rem;          /* 16px */
    --vp-space-5: 1.25rem;       /* 20px */
    --vp-space-6: 1.5rem;        /* 24px */
    --vp-space-7: 1.75rem;       /* 28px */
    --vp-space-8: 2rem;          /* 32px */
    --vp-space-10: 2.5rem;       /* 40px */
    --vp-space-12: 3rem;         /* 48px */
    --vp-space-16: 4rem;         /* 64px */
    --vp-space-20: 5rem;         /* 80px */
    --vp-space-24: 6rem;         /* 96px */
    --vp-space-32: 8rem;         /* 128px */

    /* Legacy spacing aliases */
    --vp-space-xs: var(--vp-space-1);
    --vp-space-sm: var(--vp-space-2);
    --vp-space-md: var(--vp-space-4);
    --vp-space-lg: var(--vp-space-6);
    --vp-space-xl: var(--vp-space-8);
    --vp-space-2xl: var(--vp-space-12);
    --vp-space-3xl: var(--vp-space-16);

    /* Container Sizes */
    --vp-container-sm: 640px;
    --vp-container-md: 768px;
    --vp-container-lg: 1024px;
    --vp-container-xl: 1280px;
    --vp-container-2xl: 1536px;
    --vp-container-full: 100%;

    /* Transitions - Refined timing */
    --vp-transition-fast: 0.1s cubic-bezier(0.4, 0, 0.2, 1);
    --vp-transition-normal: 0.15s cubic-bezier(0.4, 0, 0.2, 1);
    --vp-transition-slow: 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    --vp-transition-slower: 0.5s cubic-bezier(0.4, 0, 0.2, 1);

    /* Shadows - Professional depth system */
    --vp-shadow-xs: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --vp-shadow-sm: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
    --vp-shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    --vp-shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    --vp-shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    --vp-shadow-2xl: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
    --vp-shadow-inner: inset 0 2px 4px 0 rgba(0, 0, 0, 0.06);

    /* Border Radius - Consistent system */
    --vp-radius-none: 0;
    --vp-radius-sm: 0.125rem;     /* 2px */
    --vp-radius-md: 0.375rem;     /* 6px */
    --vp-radius-lg: 0.5rem;       /* 8px */
    --vp-radius-xl: 0.75rem;      /* 12px */
    --vp-radius-2xl: 1rem;        /* 16px */
    --vp-radius-3xl: 1.5rem;      /* 24px */
    --vp-radius-full: 9999px;
}

/* ==========================================================================
   Typography - Professional Fortune 500 System
   ========================================================================== */

/* Import Inter font for professional appearance */
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap');

/* Heading Styles */
h1, h2, h3, h4, h5, h6 {
    margin: 0 0 var(--vp-space-6) 0;
    font-family: var(--vp-font-family-heading);
    font-weight: var(--vp-font-weight-semibold);
    line-height: var(--vp-line-height-tight);
    color: var(--vp-text-primary);
    letter-spacing: -0.025em;
}

h1 {
    font-size: var(--vp-font-size-4xl);
    font-weight: var(--vp-font-weight-bold);
    line-height: var(--vp-line-height-none);
    letter-spacing: -0.05em;
}

h2 {
    font-size: var(--vp-font-size-3xl);
    font-weight: var(--vp-font-weight-semibold);
    line-height: var(--vp-line-height-tight);
    letter-spacing: -0.025em;
}

h3 {
    font-size: var(--vp-font-size-2xl);
    font-weight: var(--vp-font-weight-semibold);
}

h4 {
    font-size: var(--vp-font-size-xl);
    font-weight: var(--vp-font-weight-medium);
}

h5 {
    font-size: var(--vp-font-size-lg);
    font-weight: var(--vp-font-weight-medium);
}

h6 {
    font-size: var(--vp-font-size-base);
    font-weight: var(--vp-font-weight-medium);
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

/* Body Text */
p {
    margin: 0 0 var(--vp-space-4) 0;
    line-height: var(--vp-line-height-relaxed);
    color: var(--vp-text-secondary);
}

p:last-child {
    margin-bottom: 0;
}

/* Lead text for introductions */
.lead {
    font-size: var(--vp-font-size-lg);
    font-weight: var(--vp-font-weight-normal);
    line-height: var(--vp-line-height-relaxed);
    color: var(--vp-text-primary);
    margin-bottom: var(--vp-space-6);
}

/* Links */
a {
    color: var(--vp-primary);
    text-decoration: none;
    transition: all var(--vp-transition-fast);
    position: relative;
}

a:hover,
a:focus {
    color: var(--vp-primary-hover);
}

a:focus {
    outline: 2px solid var(--vp-primary-light);
    outline-offset: 2px;
}

/* Professional link underline effect */
.content a:not(.button):not(.vp-read-more) {
    border-bottom: 1px solid transparent;
    transition: border-color var(--vp-transition-fast);
}

.content a:not(.button):not(.vp-read-more):hover {
    border-bottom-color: var(--vp-primary);
}

/* Small text */
small {
    font-size: var(--vp-font-size-sm);
    color: var(--vp-text-tertiary);
}

/* Strong and emphasis */
strong, b {
    font-weight: var(--vp-font-weight-semibold);
    color: var(--vp-text-primary);
}

em, i {
    font-style: italic;
}

/* Code and preformatted text */
code {
    font-family: var(--vp-font-family-mono);
    font-size: 0.875em;
    background-color: var(--vp-background-subtle);
    padding: 0.125rem 0.25rem;
    border-radius: var(--vp-radius-sm);
    color: var(--vp-text-primary);
}

pre {
    font-family: var(--vp-font-family-mono);
    font-size: var(--vp-font-size-sm);
    background-color: var(--vp-background-subtle);
    padding: var(--vp-space-4);
    border-radius: var(--vp-radius-lg);
    overflow-x: auto;
    line-height: var(--vp-line-height-normal);
}

pre code {
    background: none;
    padding: 0;
    border-radius: 0;
}

/* Lists */
ul, ol {
    margin: 0 0 var(--vp-space-4) 0;
    padding-left: var(--vp-space-6);
}

li {
    margin-bottom: var(--vp-space-1);
    line-height: var(--vp-line-height-relaxed);
    color: var(--vp-text-secondary);
}

/* Blockquotes */
blockquote {
    margin: var(--vp-space-6) 0;
    padding: var(--vp-space-4) var(--vp-space-6);
    border-left: 4px solid var(--vp-primary);
    background-color: var(--vp-background-alt);
    border-radius: 0 var(--vp-radius-lg) var(--vp-radius-lg) 0;
}

blockquote p {
    font-size: var(--vp-font-size-lg);
    font-style: italic;
    color: var(--vp-text-primary);
    margin-bottom: var(--vp-space-2);
}

blockquote cite {
    font-size: var(--vp-font-size-sm);
    color: var(--vp-text-tertiary);
    font-style: normal;
}

/* ==========================================================================
   Professional Animations & Transitions
   ========================================================================== */

/* Page load animations */
body.loaded {
    animation: fadeIn 0.6s ease-out;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Smooth transitions for interactive elements */
a, button, input, textarea, select {
    transition: all var(--vp-transition-fast);
}

/* Professional hover effects */
.vp-hover-lift {
    transition: all var(--vp-transition-normal);
}

.vp-hover-lift:hover {
    transform: translateY(-2px);
    box-shadow: var(--vp-shadow-lg);
}

/* Focus states for accessibility */
*:focus {
    outline: 2px solid var(--vp-primary);
    outline-offset: 2px;
}

button:focus,
input:focus,
textarea:focus,
select:focus,
a:focus {
    outline: 2px solid var(--vp-primary);
    outline-offset: 2px;
}

/* ==========================================================================
   Professional Button System
   ========================================================================== */

.vp-button,
.wp-block-button__link,
input[type="submit"],
button[type="submit"] {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: var(--vp-space-2);
    padding: var(--vp-space-3) var(--vp-space-6);
    background-color: var(--vp-primary);
    color: var(--vp-text-inverse);
    border: 2px solid var(--vp-primary);
    border-radius: var(--vp-radius-lg);
    font-family: var(--vp-font-family-primary);
    font-size: var(--vp-font-size-sm);
    font-weight: var(--vp-font-weight-medium);
    text-decoration: none;
    cursor: pointer;
    transition: all var(--vp-transition-fast);
    min-height: 44px;
}

.vp-button:hover,
.wp-block-button__link:hover,
input[type="submit"]:hover,
button[type="submit"]:hover {
    background-color: var(--vp-primary-hover);
    border-color: var(--vp-primary-hover);
    transform: translateY(-1px);
    box-shadow: var(--vp-shadow-md);
}

.vp-button-secondary {
    background-color: transparent;
    color: var(--vp-primary);
    border-color: var(--vp-primary);
}

.vp-button-secondary:hover {
    background-color: var(--vp-primary);
    color: var(--vp-text-inverse);
}

.vp-button-outline {
    background-color: transparent;
    color: var(--vp-text-primary);
    border-color: var(--vp-border-dark);
}

.vp-button-outline:hover {
    background-color: var(--vp-text-primary);
    color: var(--vp-text-inverse);
    border-color: var(--vp-text-primary);
}

/* ==========================================================================
   Professional Form Elements
   ========================================================================== */

input[type="text"],
input[type="email"],
input[type="url"],
input[type="password"],
input[type="search"],
input[type="number"],
input[type="tel"],
input[type="range"],
input[type="date"],
input[type="month"],
input[type="week"],
input[type="time"],
input[type="datetime"],
input[type="datetime-local"],
input[type="color"],
textarea,
select {
    width: 100%;
    padding: var(--vp-space-3) var(--vp-space-4);
    border: 1px solid var(--vp-border);
    border-radius: var(--vp-radius-lg);
    background-color: var(--vp-background);
    color: var(--vp-text-primary);
    font-family: var(--vp-font-family-primary);
    font-size: var(--vp-font-size-sm);
    line-height: var(--vp-line-height-normal);
    transition: all var(--vp-transition-fast);
}

input:focus,
textarea:focus,
select:focus {
    border-color: var(--vp-primary);
    box-shadow: 0 0 0 3px var(--vp-primary-light);
}

input::placeholder,
textarea::placeholder {
    color: var(--vp-text-tertiary);
}

/* ==========================================================================
   Layout Components
   ========================================================================== */

.vp-container {
    width: 100%;
    max-width: var(--vp-container-xl);
    margin: 0 auto;
    padding: 0 var(--vp-space-4);
}

.vp-grid {
    display: grid;
    gap: var(--vp-space-6);
}

.vp-flex {
    display: flex;
    gap: var(--vp-space-4);
}

/* ==========================================================================
   Professional Responsive Design System
   ========================================================================== */

/* Enhanced sticky header behavior */
.site-header.scrolled {
    background-color: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    box-shadow: var(--vp-shadow-md);
}

.site-header {
    transition: all var(--vp-transition-normal);
}

/* Professional loading states */
.vp-loading {
    position: relative;
    overflow: hidden;
}

.vp-loading::after {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
    animation: loading 1.5s infinite;
}

@keyframes loading {
    0% { left: -100%; }
    100% { left: 100%; }
}

/* Responsive breakpoints with enhanced typography */
@media (min-width: 640px) {
    .vp-container {
        padding: 0 var(--vp-space-6);
    }

    :root {
        --vp-font-size-4xl: 2.75rem;
        --vp-font-size-3xl: 2.125rem;
    }
}

@media (min-width: 768px) {
    .vp-container {
        padding: 0 var(--vp-space-8);
    }

    :root {
        --vp-font-size-4xl: 3rem;
        --vp-font-size-3xl: 2.25rem;
        --vp-font-size-5xl: 3.5rem;
        --vp-font-size-6xl: 4rem;
    }
}

@media (min-width: 1024px) {
    .vp-container {
        padding: 0 var(--vp-space-8);
    }

    :root {
        --vp-font-size-4xl: 3.5rem;
        --vp-font-size-5xl: 4rem;
        --vp-font-size-6xl: 4.5rem;
    }
}

@media (min-width: 1280px) {
    :root {
        --vp-font-size-4xl: 4rem;
        --vp-font-size-5xl: 4.5rem;
        --vp-font-size-6xl: 5rem;
    }
}

/* High-resolution display optimizations */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
    .custom-logo,
    .footer-logo {
        image-rendering: -webkit-optimize-contrast;
        image-rendering: crisp-edges;
    }
}

/* Dark mode support (system preference) */
@media (prefers-color-scheme: dark) {
    :root {
        --vp-background: #1a202c;
        --vp-background-alt: #2d3748;
        --vp-background-subtle: #4a5568;
        --vp-text-primary: #f7fafc;
        --vp-text-secondary: #e2e8f0;
        --vp-text-tertiary: #cbd5e0;
        --vp-border: #4a5568;
        --vp-border-light: #2d3748;
    }

    .site-header.scrolled {
        background-color: rgba(26, 32, 44, 0.95);
    }
}

/* Reduced motion preferences */
@media (prefers-reduced-motion: reduce) {
    *,
    *::before,
    *::after {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
        scroll-behavior: auto !important;
    }

    .vp-hover-lift:hover {
        transform: none;
    }
}

/* ==========================================================================
   Utility Classes
   ========================================================================== */

.vp-sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
}

.vp-text-center { text-align: center; }
.vp-text-left { text-align: left; }
.vp-text-right { text-align: right; }

.vp-hidden { display: none; }
.vp-block { display: block; }
.vp-inline-block { display: inline-block; }

/* ==========================================================================
   WordPress Core Styles
   ========================================================================== */

.alignleft {
    float: left;
    margin: 0 var(--vp-space-lg) var(--vp-space-md) 0;
}

.alignright {
    float: right;
    margin: 0 0 var(--vp-space-md) var(--vp-space-lg);
}

.aligncenter {
    display: block;
    margin: 0 auto var(--vp-space-md);
    text-align: center;
}

.wp-caption {
    max-width: 100%;
    margin-bottom: var(--vp-space-md);
}

.wp-caption img {
    display: block;
    margin: 0 auto;
    max-width: 100%;
    height: auto;
}

.wp-caption-text {
    font-size: var(--vp-font-size-sm);
    color: var(--vp-text-secondary);
    text-align: center;
    margin-top: var(--vp-space-sm);
}

/* ==========================================================================
   Print Styles
   ========================================================================== */

@media print {
    *,
    *::before,
    *::after {
        background: transparent !important;
        color: #000 !important;
        box-shadow: none !important;
        text-shadow: none !important;
    }

    a,
    a:visited {
        text-decoration: underline;
    }

    img {
        page-break-inside: avoid;
    }

    h2,
    h3,
    p {
        orphans: 3;
        widows: 3;
    }

    h2,
    h3 {
        page-break-after: avoid;
    }
}