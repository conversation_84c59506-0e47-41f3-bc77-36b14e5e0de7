<?php
/**
 * Functions which enhance the theme by hooking into WordPress
 *
 * @package VlogPress
 * @since 1.0.0
 */





/**
 * Customize excerpt length
 */
function vlogpress_excerpt_length( $length ) {
    if ( is_admin() ) {
        return $length;
    }

    return 25;
}
add_filter( 'excerpt_length', 'vlogpress_excerpt_length', 999 );

/**
 * Customize excerpt more string
 */
function vlogpress_excerpt_more( $more ) {
    if ( is_admin() ) {
        return $more;
    }

    return '&hellip;';
}
add_filter( 'excerpt_more', 'vlogpress_excerpt_more' );

/**
 * Add custom image sizes to media library
 */
function vlogpress_custom_image_sizes( $sizes ) {
    return array_merge( $sizes, array(
        'vlogpress-featured' => __( 'VlogPress Featured', 'vlogpress' ),
        'vlogpress-thumbnail' => __( 'VlogPress Thumbnail', 'vlogpress' ),
        'vlogpress-small' => __( 'VlogPress Small', 'vlogpress' ),
    ) );
}
add_filter( 'image_size_names_choose', 'vlogpress_custom_image_sizes' );

/**
 * Enqueue block editor styles
 */
function vlogpress_block_editor_styles() {
    wp_enqueue_style( 'vlogpress-block-editor-styles', get_template_directory_uri() . '/css/editor-style.css', array(), VLOGPRESS_VERSION );
}
add_action( 'enqueue_block_editor_assets', 'vlogpress_block_editor_styles' );

/**
 * Add theme support for Gutenberg wide images in your theme
 */
function vlogpress_gutenberg_support() {
    add_theme_support( 'align-wide' );
    add_theme_support( 'wp-block-styles' );
}
add_action( 'after_setup_theme', 'vlogpress_gutenberg_support' );

/**
 * Filter the categories archive widget to add a span around post count
 */
function vlogpress_cat_count_span( $links ) {
    $links = str_replace( '</a> (', '</a> <span class="count">(', $links );
    $links = str_replace( ')', ')</span>', $links );
    return $links;
}
add_filter( 'wp_list_categories', 'vlogpress_cat_count_span' );

/**
 * Filter the archives widget to add a span around post count
 */
function vlogpress_archive_count_span( $links ) {
    $links = str_replace( '</a>&nbsp;(', '</a> <span class="count">(', $links );
    $links = str_replace( ')', ')</span>', $links );
    return $links;
}
add_filter( 'get_archives_link', 'vlogpress_archive_count_span' );

/**
 * Add custom logo support with better defaults
 */
function vlogpress_custom_logo_setup() {
    $defaults = array(
        'height'               => 100,
        'width'                => 400,
        'flex-height'          => true,
        'flex-width'           => true,
        'header-text'          => array( 'site-title', 'site-description' ),
        'unlink-homepage-logo' => true,
    );
    add_theme_support( 'custom-logo', $defaults );
}
add_action( 'after_setup_theme', 'vlogpress_custom_logo_setup' );

/**
 * Modify tag cloud widget font sizes
 */
function vlogpress_tag_cloud_sizes( $args ) {
    $args['smallest'] = 0.8;
    $args['largest'] = 1.2;
    $args['unit'] = 'rem';
    return $args;
}
add_filter( 'widget_tag_cloud_args', 'vlogpress_tag_cloud_sizes' );

/**
 * Add custom search form
 */
function vlogpress_search_form( $form ) {
    $form = '<form role="search" method="get" class="search-form" action="' . home_url( '/' ) . '">
        <label>
            <span class="vp-sr-only">' . _x( 'Search for:', 'label', 'vlogpress' ) . '</span>
            <input type="search" class="search-field" placeholder="' . esc_attr_x( 'Search &hellip;', 'placeholder', 'vlogpress' ) . '" value="' . get_search_query() . '" name="s" />
        </label>
        <button type="submit" class="search-submit">
            <span class="vp-sr-only">' . _x( 'Search', 'submit button', 'vlogpress' ) . '</span>
            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M21 21L16.514 16.506L21 21ZM19 10.5C19 15.194 15.194 19 10.5 19C5.806 19 2 15.194 2 10.5C2 5.806 5.806 2 10.5 2C15.194 2 19 5.806 19 10.5Z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            </svg>
        </button>
    </form>';

    return $form;
}
add_filter( 'get_search_form', 'vlogpress_search_form' );

/**
 * Modify the main query for better content mixing
 */
function vlogpress_modify_main_query( $query ) {
    if ( ! is_admin() && $query->is_main_query() ) {
        if ( is_home() ) {
            // Include both posts and vlogs on the homepage
            $query->set( 'post_type', array( 'post', 'vlog' ) );
        }
    }
}
add_action( 'pre_get_posts', 'vlogpress_modify_main_query' );

/**
 * Add viewport meta tag for mobile responsiveness
 */
function vlogpress_viewport_meta() {
    echo '<meta name="viewport" content="width=device-width, initial-scale=1">';
}
add_action( 'wp_head', 'vlogpress_viewport_meta', 1 );

/**
 * Remove unnecessary WordPress head elements
 */
function vlogpress_clean_head() {
    remove_action( 'wp_head', 'wp_generator' );
    remove_action( 'wp_head', 'wlwmanifest_link' );
    remove_action( 'wp_head', 'rsd_link' );
    remove_action( 'wp_head', 'wp_shortlink_wp_head' );
}
add_action( 'init', 'vlogpress_clean_head' );