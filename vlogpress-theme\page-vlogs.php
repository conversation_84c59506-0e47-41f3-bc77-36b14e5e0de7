<?php
/**
 * Template for displaying all vlogs
 * 
 * Template Name: Vlogs Page
 *
 * @package VlogPress
 * @since 1.0.0
 */

get_header(); ?>

<main id="main" class="site-main vlogs-page" role="main">
    <div class="vp-container">

        <header class="page-header vlogs-header">
            <h1 class="page-title">
                <svg class="page-icon" width="32" height="32" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M8 5V19L19 12L8 5Z" fill="currentColor"/>
                </svg>
                <?php _e( 'Video Content', 'vlogpress' ); ?>
            </h1>
            <p class="page-description">
                <?php _e( 'Discover our latest video content, tutorials, and vlogs.', 'vlogpress' ); ?>
            </p>
        </header>

        <?php
        // Custom query for vlogs only
        $paged = ( get_query_var( 'paged' ) ) ? get_query_var( 'paged' ) : 1;
        
        $vlogs_query = new WP_Query( array(
            'post_type'      => 'vlog',
            'post_status'    => 'publish',
            'posts_per_page' => get_option( 'posts_per_page' ),
            'paged'          => $paged,
            'meta_query'     => array(
                'relation' => 'OR',
                array(
                    'key'     => '_vp_video_url',
                    'compare' => 'EXISTS'
                ),
                array(
                    'key'     => '_vp_video_url',
                    'value'   => '',
                    'compare' => '!='
                )
            )
        ) );

        if ( $vlogs_query->have_posts() ) : ?>

            <div class="vlogs-filters">
                <div class="filter-buttons">
                    <button class="filter-btn active" data-filter="all"><?php _e( 'All Videos', 'vlogpress' ); ?></button>
                    <?php
                    // Get categories used by vlogs
                    $vlog_categories = get_terms( array(
                        'taxonomy' => 'category',
                        'hide_empty' => true,
                        'object_ids' => wp_list_pluck( $vlogs_query->posts, 'ID' )
                    ) );
                    
                    if ( ! empty( $vlog_categories ) && ! is_wp_error( $vlog_categories ) ) :
                        foreach ( $vlog_categories as $category ) : ?>
                            <button class="filter-btn" data-filter="<?php echo esc_attr( $category->slug ); ?>">
                                <?php echo esc_html( $category->name ); ?>
                            </button>
                        <?php endforeach;
                    endif; ?>
                </div>
            </div>

            <div class="vp-content-grid vlogs-grid">
                <?php while ( $vlogs_query->have_posts() ) : $vlogs_query->the_post(); ?>

                    <article id="vlog-<?php the_ID(); ?>" <?php post_class( 'vp-content-card vlog-card' ); ?> data-categories="<?php echo esc_attr( implode( ' ', wp_get_post_categories( get_the_ID(), array( 'fields' => 'slugs' ) ) ) ); ?>">

                        <?php if ( has_post_thumbnail() ) : ?>
                            <div class="vp-card-thumbnail vlog-thumbnail">
                                <a href="<?php the_permalink(); ?>" aria-hidden="true" tabindex="-1">
                                    <?php the_post_thumbnail( 'medium_large', array( 'alt' => the_title_attribute( array( 'echo' => false ) ) ) ); ?>
                                </a>

                                <div class="vp-video-overlay">
                                    <svg class="vp-play-icon" width="48" height="48" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                        <path d="M8 5V19L19 12L8 5Z" fill="currentColor"/>
                                    </svg>
                                </div>

                                <?php
                                $video_duration = get_post_meta( get_the_ID(), '_vp_video_duration', true );
                                if ( $video_duration ) : ?>
                                    <div class="vp-video-duration"><?php echo esc_html( $video_duration ); ?></div>
                                <?php endif; ?>
                            </div>
                        <?php endif; ?>

                        <div class="vp-card-content">
                            <header class="vp-card-header">
                                <?php the_title( '<h2 class="vp-card-title"><a href="' . esc_url( get_permalink() ) . '" rel="bookmark">', '</a></h2>' ); ?>

                                <div class="vp-card-meta">
                                    <span class="vp-content-type vp-type-video">
                                        <?php _e( 'Video', 'vlogpress' ); ?>
                                    </span>

                                    <time class="vp-published-date" datetime="<?php echo esc_attr( get_the_date( 'c' ) ); ?>">
                                        <?php echo esc_html( get_the_date() ); ?>
                                    </time>

                                    <?php if ( $video_duration ) : ?>
                                        <span class="vp-video-duration-meta">
                                            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                <circle cx="12" cy="12" r="10" stroke="currentColor" stroke-width="2"/>
                                                <polyline points="12,6 12,12 16,14" stroke="currentColor" stroke-width="2"/>
                                            </svg>
                                            <?php echo esc_html( $video_duration ); ?>
                                        </span>
                                    <?php endif; ?>

                                    <?php
                                    $view_count = get_post_meta( get_the_ID(), '_vp_view_count', true );
                                    if ( $view_count ) : ?>
                                        <span class="vp-view-count">
                                            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                <path d="M1 12S5 4 12 4s11 8 11 8-4 8-11 8S1 12 1 12z" stroke="currentColor" stroke-width="2"/>
                                                <circle cx="12" cy="12" r="3" stroke="currentColor" stroke-width="2"/>
                                            </svg>
                                            <?php printf( _n( '%s view', '%s views', $view_count, 'vlogpress' ), number_format_i18n( $view_count ) ); ?>
                                        </span>
                                    <?php endif; ?>
                                </div>
                            </header>

                            <div class="vp-card-excerpt">
                                <?php the_excerpt(); ?>
                            </div>

                            <footer class="vp-card-footer">
                                <a href="<?php the_permalink(); ?>" class="vp-read-more vp-watch-video">
                                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                        <path d="M8 5V19L19 12L8 5Z" fill="currentColor"/>
                                    </svg>
                                    <?php _e( 'Watch Video', 'vlogpress' ); ?>
                                    <span class="vp-sr-only"><?php printf( __( 'about %s', 'vlogpress' ), get_the_title() ); ?></span>
                                </a>

                                <?php if ( has_category() ) : ?>
                                    <div class="vp-card-terms">
                                        <?php
                                        $categories = get_the_category();
                                        if ( $categories ) {
                                            echo '<span class="vp-category">' . esc_html( $categories[0]->name ) . '</span>';
                                        }
                                        ?>
                                    </div>
                                <?php endif; ?>
                            </footer>
                        </div>
                    </article>

                <?php endwhile; ?>
            </div>

            <?php
            // Custom pagination for vlogs
            $big = 999999999; // need an unlikely integer
            echo paginate_links( array(
                'base'      => str_replace( $big, '%#%', esc_url( get_pagenum_link( $big ) ) ),
                'format'    => '?paged=%#%',
                'current'   => max( 1, get_query_var( 'paged' ) ),
                'total'     => $vlogs_query->max_num_pages,
                'mid_size'  => 2,
                'prev_text' => __( '&larr; Previous', 'vlogpress' ),
                'next_text' => __( 'Next &rarr;', 'vlogpress' ),
                'type'      => 'list',
                'class'     => 'vp-pagination vlogs-pagination',
            ) );
            ?>

        <?php else : ?>

            <section class="vp-no-results vlogs-no-results">
                <header class="page-header">
                    <h2 class="page-title"><?php _e( 'No videos found', 'vlogpress' ); ?></h2>
                </header>

                <div class="page-content">
                    <p><?php _e( 'No video content has been published yet. Check back soon for new videos!', 'vlogpress' ); ?></p>
                    
                    <?php if ( current_user_can( 'publish_posts' ) ) : ?>
                        <p>
                            <a href="<?php echo esc_url( admin_url( 'post-new.php?post_type=vlog' ) ); ?>" class="vp-button">
                                <?php _e( 'Add Your First Video', 'vlogpress' ); ?>
                            </a>
                        </p>
                    <?php endif; ?>
                </div>
            </section>

        <?php endif; ?>

        <?php wp_reset_postdata(); ?>

    </div>
</main>

<?php
get_sidebar();
get_footer();
